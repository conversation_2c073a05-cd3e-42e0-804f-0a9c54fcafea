class AddEmailSerializerPerformanceIndexes < ActiveRecord::Migration[6.1]
  disable_ddl_transaction!

  def up
    # Critical Priority Indexes - These provide the biggest performance gains

    # 1. GIN index for read_by array operations (CRITICAL)
    # Fixes major performance issue in preload_email_thread_read_details
    # Array containment operations (@>) require GIN indexes for efficiency
    add_index :emails, :read_by, 
              using: :gin, 
              algorithm: :concurrently,
              name: 'idx_emails_read_by_gin',
              if_not_exists: true

    # 2. Index for email_look_ups related field filtering
    # Optimizes preload_related_entities query
    add_index :email_look_ups, [:tenant_id, :related, :email_id], 
              where: "related = true",
              algorithm: :concurrently,
              name: 'idx_email_look_ups_related',
              if_not_exists: true

    # 3. Index for look_ups entity_type filtering
    # Optimizes JOIN conditions in preload_related_entities
    add_index :look_ups, [:entity_type, :deleted, :tenant_id], 
              where: "deleted = false",
              algorithm: :concurrently,
              name: 'idx_look_ups_entity_type_deleted',
              if_not_exists: true

    # High Priority Indexes - Significant performance improvements

    # 4. Composite index for recipient_type filtering
    # Optimizes preload_to_details query
    add_index :email_look_ups, [:tenant_id, :email_id, :recipient_type], 
              algorithm: :concurrently,
              name: 'idx_email_look_ups_recipient_type',
              if_not_exists: true

    # 5. Composite index for track logs optimization
    # Optimizes DISTINCT ON queries in preload_track_logs
    add_index :email_track_logs, [:tenant_id, :email_id, :created_at], 
              order: { created_at: :desc },
              algorithm: :concurrently,
              name: 'idx_email_track_logs_composite',
              if_not_exists: true

    # 6. Composite index for link logs optimization
    # Optimizes DISTINCT ON queries in preload_link_logs
    add_index :email_link_logs, [:tenant_id, :email_id, :created_at], 
              order: { created_at: :desc },
              algorithm: :concurrently,
              name: 'idx_email_link_logs_composite',
              if_not_exists: true
  end

  def down
    # Remove indexes in reverse order
    remove_index :email_link_logs, name: 'idx_email_link_logs_composite', if_exists: true
    remove_index :email_track_logs, name: 'idx_email_track_logs_composite', if_exists: true
    remove_index :email_look_ups, name: 'idx_email_look_ups_recipient_type', if_exists: true
    remove_index :look_ups, name: 'idx_look_ups_entity_type_deleted', if_exists: true
    remove_index :email_look_ups, name: 'idx_email_look_ups_related', if_exists: true
    remove_index :emails, name: 'idx_emails_read_by_gin', if_exists: true
  end
end
