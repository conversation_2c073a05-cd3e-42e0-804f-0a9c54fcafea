class AddEmailSerializerSecondaryIndexes < ActiveRecord::Migration[6.1]
  disable_ddl_transaction!

  def up
    # Medium Priority Indexes - Additional performance improvements
    # Deploy these after monitoring the impact of the critical indexes

    # 1. Composite index for subject queries
    # Optimizes DISTINCT ON with ORDER BY in preload_subjects_for_thread
    add_index :emails, [:tenant_id, :email_thread_id, :created_at], 
              order: { created_at: :desc },
              algorithm: :concurrently,
              name: 'idx_emails_thread_created_at',
              if_not_exists: true

    # 2. Composite index for attachment count optimization
    # Optimizes SUM() operations in preload_emails_attachments_count
    add_index :emails, [:tenant_id, :email_thread_id, :external_attachment_count], 
              algorithm: :concurrently,
              name: 'idx_emails_attachment_count',
              if_not_exists: true

    # 3. Composite index for look_ups tenant + id optimization
    # Optimizes batch lookups in preload_to_details
    add_index :look_ups, [:tenant_id, :id], 
              where: "deleted = false",
              algorithm: :concurrently,
              name: 'idx_look_ups_tenant_id_composite',
              if_not_exists: true
  end

  def down
    # Remove indexes in reverse order
    remove_index :look_ups, name: 'idx_look_ups_tenant_id_composite', if_exists: true
    remove_index :emails, name: 'idx_emails_attachment_count', if_exists: true
    remove_index :emails, name: 'idx_emails_thread_created_at', if_exists: true
  end
end
