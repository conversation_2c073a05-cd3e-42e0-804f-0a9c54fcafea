# frozen_string_literal: true
require 'json'
class LogFormatter
  def call(severity, time, progname, msg = '')
    return '' if msg.blank?

    line = { "context": "default", "timestamp": time.strftime("%Y-%m-%d %H:%M:%S.%L"),
    	    "level": severity, "progname": "#{progname}" }.merge(processed_message(msg))

    return "#{line.to_json}\n"
  end

  private

  def processed_message(msg)
    msg = msg.encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
    msg = JSON.parse(msg)
    return msg if msg.is_a?(Hash)

  rescue
    { message: msg.strip }
  end
end