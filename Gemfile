source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '2.7.1'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 6.1.7.10'

# Use <PERSON><PERSON> as the app server
gem 'puma', '~> 6.4.3'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
# gem 'jbuilder', '~> 2.7'
# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'
# Use Active Model has_secure_password
# gem 'bcrypt', '~> 3.1.7'
gem 'jwt'
gem 'simple_command'
gem 'jbuilder'
gem 'rest-client', require: false
# Use Active Storage variant
# gem 'image_processing', '~> 1.2'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.2', require: false

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
# gem 'rack-cors'

gem 'pg'
gem 'oj'
gem 'bunny'
gem 'connection_pool'
gem 'prometheus-client'
gem 'rswag-api'
gem 'rswag-ui'
gem 'will_paginate'

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: [:mri, :mingw, :x64_mingw]
  gem 'faker'
  gem 'factory_bot_rails'
  gem 'rspec-rails', '~> 4.0.0'
  gem 'rspec-collection_matchers'
  gem 'rspec-mocks'
  gem 'shoulda-matchers'
  gem 'simplecov', require: false
  gem 'database_cleaner'
  gem 'bunny-mock', require: false
  gem 'dotenv-rails', groups: [:development, :test]
  gem 'webmock'
  gem 'rswag-specs'
  gem 'pry'
end

group :development do
  gem 'listen', '~> 3.2'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]

gem 'google-api-client'
gem 'rmail'
gem 'rack-cors', "~> 2.0.2"
gem 'aws-sdk-s3', '~> 1'
gem 'silencer'
gem 'sidekiq'
gem 'redis-namespace'
gem "rexml", "~> 3.3.9"
gem "webrick", "~> 1.8.2"

