require 'rails_helper'

RSpec.describe FetchOutlookMessages do
  describe "#call" do
    before do
      @payload = [
        {
          "subscriptionId": "sub-123",
          "subscriptionExpirationDateTime": "2021-04-29T11:23:45.9356913-07:00",
          "changeType": "created",
          "resource": "Users/user-123/Messages/message-123",
          "resourceData": {
            "@odata.type": "#Microsoft.Graph.Message",
            "@odata.id": "Users/user-123/Messages/message-123",
            "@odata.etag": "W/\"CQAAABYAAAAKompOuPMdRJZUwPhAtVKQAACsxuzK\"",
            "id": "message-123"
          },
          "clientState": "secretClientValue",
          "tenantId": "xyz"
        }
      ]

      @payload_with_different_thread = [
        {
          "subscriptionId": "sub-1234",
          "subscriptionExpirationDateTime": "2021-04-29T11:23:45.9356913-07:00",
          "changeType": "created",
          "resource": "Users/user-123/Messages/message-123",
          "resourceData": {
            "@odata.type": "#Microsoft.Graph.Message",
            "@odata.id": "Users/user-123/Messages/message-1234",
            "@odata.etag": "W/\"CQAAABYAAAAKompOuPMdRJZUwPhAtVKQAACsxuzK\"",
            "id": "message-1234"
          },
          "clientState": "secretClientValue",
          "tenantId": "xyz"
        }
      ]

      @sample_message = {
        "id": "message-123",
        "createdDateTime": "2021-05-04T19:08:19Z",
        "receivedDateTime": "2021-05-04T19:08:21Z",
        "sentDateTime": "2021-05-04T19:08:19Z",
        "hasAttachments": false,
        "subject": "hello",
        "internetMessageId": "im1",
        "conversationId": "conversation-1",
        "body": {
          "contentType": "html",
          "content": "<html><head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><meta content=\"text/html; charset=iso-8859-1\"><style type=\"text/css\" style=\"display:none\">\r\n<!--\r\np\r\n\t{margin-top:0;\r\n\tmargin-bottom:0}\r\n-->\r\n</style></head><body dir=\"ltr\"><div style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">hi</div></body></html>"
        },
        "from": {
          "emailAddress": {
            "name": "Test user",
            "address": "<EMAIL>"
          }
        },
        "toRecipients": [
          {
            "emailAddress": {
              "name": "to user",
              "address": "<EMAIL>"
            }
          }
        ],
        "ccRecipients": [{
          "emailAddress": {
            "name": "cc user",
            "address": "<EMAIL>"
          }
        }],
        "bccRecipients": [{
          "emailAddress": {
            "name": "bcc user",
            "address": "<EMAIL>"
          }
        }],
        "replyTo": [],
      }

      @sample_message_1 = {
        "id": "message-1234",
        "createdDateTime": "2021-05-04T19:08:19Z",
        "receivedDateTime": "2021-05-04T19:08:21Z",
        "sentDateTime": "2021-05-04T19:08:19Z",
        "hasAttachments": false,
        "subject": "hello",
        "internetMessageId": "im1",
        "conversationId": "conversation-2",
        "body": {
          "contentType": "html",
          "content": "<html><head>\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><meta content=\"text/html; charset=iso-8859-1\"><style type=\"text/css\" style=\"display:none\">\r\n<!--\r\np\r\n\t{margin-top:0;\r\n\tmargin-bottom:0}\r\n-->\r\n</style></head><body dir=\"ltr\"><div style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">hi</div></body></html>"
        },
        "from": {
          "emailAddress": {
            "name": "Test user",
            "address": "<EMAIL>"
          }
        },
        "toRecipients": [
          {
            "emailAddress": {
              "name": "to user",
              "address": "<EMAIL>"
            }
          }
        ],
        "ccRecipients": [{
          "emailAddress": {
            "name": "cc user",
            "address": "<EMAIL>"
          }
        }],
        "bccRecipients": [{
          "emailAddress": {
            "name": "bcc user",
            "address": "<EMAIL>"
          }
        }],
        "replyTo": [],
      }

      @sample_attachments = {
        "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#users('user-123')/messages('message-123')/attachments",
        "value": [
          {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "id": "attachment-1",
            "name": "test-file.text",
            "contentType": "text/plain",
            "isInline": false,
            "contentId": "f_kod7qs6z0",
            "contentBytes": "dGVzdCBmaWxlCg=="
          },
          {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "id": "attachment-2",
            "name": "test-file.png",
            "contentType": "image/png",
            "isInline": true,
            "contentId": "f_kod7qywo1",
            "contentBytes": "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABxUlEQVR42mNkQABNPj6+NE4ggAn8+/ef4c/fvww/f/5k+Pb1yzSg0CUGNMAIpXmio6PvTZw8RRQk9B+k+e8/hm8/fjI8f/WW4dGzlwx7Ni5/M2fOHBeg1EVsBpiuXrP2lIm5JYrp//79Y/j+4xfDh89fGZRlJRhqqytBhjgjuwRmgNmatetOWljZoLsQbMinL98Z+Hg4weyWpvq3c2bPdgVKnUczYO1JWzsHBmzgx8/fDD9//2FgZgJ67/9/hvy83LdbN63XB0o9hRsA9MJJJ2cXBlzgLyhMvv9geP76HcOq1WsY+tvrzYHCp1AMcHNzx2nAfyAExQooYDds2MiQk5GCagDQ1JNeXl4MhMBfYDhs2rSZITY6EtWAlUAD/Hx9CBoADAKwARHhoWgGrFp9MiDAn6ABIADyQngYmgErVq46GRwURJQBa9etA7ogDNWA5StWngwLDSHKAFAsREaEoxqwdPmKk5HhYUQZsHzlKoboyAgUA0wXLl56Ki4miigDFi1ZxhAfG20GZJ6GZyb/gMB702fMFOXgYMer+QcwHWRmpL/euGG9EpD7hRFJTpOHhyeNgwORnbEb8P37ly9fZgGZ10F8AL8PzRGGBrLfAAAAAElFTkSuQmCC"
          }
        ]
      }
    end

    context 'when valid webhook event payload as input' do
      before do
        @user = create(:user, id: 12, tenant: create(:tenant, id: 14) )
        connected_account = create(:connected_account, subscription_id: 'sub-123',  expires_at: (Time.now + 1.hour).to_i, user: @user)
        contact_search_response = { matched: [{ :entity=>"contact_1", :email=>"<EMAIL>", :name=>"Contact1 test", :tenant_id => @user.tenant_id },
                                              { :entity=>"contact_2", :email=>"<EMAIL>", :name=>"Contact2 test", :tenant_id => @user.tenant_id}],
        unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id },
                    { entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        lead_search_response = { matched: [{ :entity=>"lead_1", :email=>"<EMAIL>", :name=>"lead1 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        user_search_response = { matched: [{ :entity=>"user_12", :email=>"<EMAIL>", :name=>"user12 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [] }                        
        @user_search_response_for_connected_account = {
          matched: [{ :entity=>"user_12", :email=>connected_account.email, :name=>"user12 test", :tenant_id => @user.tenant_id }],
          unmatched: []
        }
        allow(SearchContacts).to receive_message_chain([:call, :result]).and_return(contact_search_response)
        allow(SearchLeads).to receive_message_chain([:call, :result]).and_return(lead_search_response)
        allow(SearchUsers).to receive_message_chain([:call, :result]).and_return(user_search_response)

        allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-123').and_return(@sample_message)
        @sample_message_2 = @sample_message.dup
        @sample_message_2[:id] = 'message-890'
        @sample_message_3 = @sample_message.dup
        @sample_message_3[:id] = 'message-999'
        @sample_message_3[:from] = {
          "emailAddress": {
            "name": "Test user",
            "address": connected_account.email
          }
        }
        allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-890').and_return(@sample_message_2)
        allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-999').and_return(@sample_message_3)
        stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-123/attachments").
          to_return(status: 200, body: @sample_attachments.to_json, headers: {})

        stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-890/attachments").
          to_return(status: 200, body: @sample_attachments.to_json, headers: {})
        allow(UploadFileToS3).to receive(:call).and_return(nil)

        stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-999/attachments").
          to_return(status: 200, body: @sample_attachments.to_json, headers: {})
        allow(UploadFileToS3).to receive(:call).and_return(nil)
      end

      context 'new mail' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailReceivedRelatedToEntity))
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2))
        end

        it 'saves message correctly' do
          expect(Email.count).to be_zero
          FetchOutlookMessages.call(@payload)
          email = Email.first

          expect(email.status).to eq('received')
          expect(email.subject).to eq(@sample_message[:subject])
          expect(email.body).to eq(@sample_message[:body][:content])
          expect(email.from).to eq(@sample_message[:from][:emailAddress][:address])
          expect(email.to).to eq([@sample_message[:toRecipients].first[:emailAddress][:address]])
          expect(email.cc).to eq([@sample_message[:ccRecipients].first[:emailAddress][:address]])
          expect(email.bcc).to eq([@sample_message[:bccRecipients].first[:emailAddress][:address]])
        end

        it 'saves related entities correctly' do
          FetchOutlookMessages.call(@payload)
          email = Email.first

          expect(email.related_to.map(&:entity)).to match_array(['contact_1', 'contact_2', 'lead_1'])
        end

        it 'stores recipients entities correctly' do
          FetchOutlookMessages.call(@payload)
          email = Email.first

          expect(email.to_recipients.map(&:email)).to eq(['<EMAIL>'])
          expect(email.to_recipients.map(&:entity)).to eq(['user_12'])
          expect(email.cc_recipients.map(&:email)).to eq(['<EMAIL>'])
          expect(email.cc_recipients.map(&:entity)).to eq(['contact_1'])
          expect(email.bcc_recipients.map(&:email)).to eq(['<EMAIL>'])
          expect(email.bcc_recipients.map(&:entity)).to eq(['contact_2'])
          expect(email.sender.email).to eq('<EMAIL>')
          expect(email.sender.entity).to eq('lead_1')
        end

        it 'stores attachments successfully' do
          FetchOutlookMessages.call(@payload)
          email = Email.first
          expect(email.attachments.count).to eq(@sample_attachments[:value].count)
          expect(email.external_attachment_count).to eq(1)

          # 1st attachment
          expect(email.attachments.first.file_name).to include(@sample_attachments[:value].first[:name].split('.').first)
          expect(email.attachments.first.inline).to eq(@sample_attachments[:value].first[:isInline])
          expect(email.attachments.first.content_id).to eq(@sample_attachments[:value].first[:contentId])

          # 2nd attachment
          expect(email.attachments.last.file_name).to include(@sample_attachments[:value].last[:name].split('.').first)
          expect(email.attachments.last.inline).to eq(@sample_attachments[:value].last[:isInline])
          expect(email.attachments.last.content_id).to eq(@sample_attachments[:value].last[:contentId])
        end

        it 'publishes email received event successfully' do
          FetchOutlookMessages.call(@payload)
        end

        context 'when received multiple webooks for same message webook with different thread' do
          before do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
          end

          it 'publishes email received event only once' do
            create(:connected_account, subscription_id: 'sub-1234',  expires_at: (Time.now + 1.hour).to_i, user: @user)
            allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-1234').and_return(@sample_message_1)
            stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-1234/attachments").
              to_return(status: 200, body: @sample_attachments.to_json, headers: {})

            expect(Email.count).to be_zero
            FetchOutlookMessages.call(@payload)
            FetchOutlookMessages.call(@payload_with_different_thread)
            expect(Email.count).to eq(2)
          end
        end

        context 'when from email is same as connected email' do
          before do
            allow(SearchUsers).to receive_message_chain([:call, :result]).and_return(@user_search_response_for_connected_account)
          end

          it 'stores status as sent' do
            @new_payload = @payload.dup
            @new_payload[0][:resourceData][:id]  = 'message-999'
            expect(Email.count).to be_zero
            FetchOutlookMessages.call(@new_payload)
            email = Email.first

            expect(email.status).to eq('sent')
            expect(email.subject).to eq(@sample_message[:subject])
            expect(email.body).to eq(@sample_message[:body][:content])
            expect(email.from).to eq(@sample_message_3[:from][:emailAddress][:address])
            expect(email.to).to eq([@sample_message[:toRecipients].first[:emailAddress][:address]])
            expect(email.cc).to eq([@sample_message[:ccRecipients].first[:emailAddress][:address]])
            expect(email.bcc).to eq([@sample_message[:bccRecipients].first[:emailAddress][:address]])
          end
        end
      end

      context 'when email is bounced notification' do
        before do
          allow(BounceProcessor).to receive(:call).and_return({ is_bounced: true })
        end

        it 'should NOT publish any events' do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailReceivedRelatedToEntity))
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailAction))
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2))
          FetchOutlookMessages.call(@payload)
        end
      end

      context 'reply on existing email' do
        before(:each) do
          FetchOutlookMessages.call(@payload)
          @new_payload = @payload.dup
          @new_payload[0][:resourceData][:id]  = 'message-890'
        end

        it 'associates mail with same email thread' do
          FetchOutlookMessages.call(@new_payload)
          expect(EmailThread.count).to eq 1
          expect(EmailThread.last.emails.count).to eq 2
        end

        it 'saves reply correctly' do
          FetchOutlookMessages.call(@new_payload)
          expect(Email.count).to eq 2
          email = Email.last
          expect(email.source_id).to eq(@sample_message_2[:id])
          expect(email.subject).to eq(@sample_message[:subject])
          expect(email.body).to eq(@sample_message[:body][:content])
          expect(email.from).to eq(@sample_message[:from][:emailAddress][:address])
          expect(email.to).to eq([@sample_message[:toRecipients].first[:emailAddress][:address]])
          expect(email.cc).to eq([@sample_message[:ccRecipients].first[:emailAddress][:address]])
          expect(email.bcc).to eq([@sample_message[:bccRecipients].first[:emailAddress][:address]])
        end

        it 'associate all deals on email which were present on first email' do
          email = Email.first
          deal1 = LookUp.create(name: 'deal-test-123', entity: 'deal_123456', entity_id: '123456', entity_type: 'deal', tenant_id: @user.tenant_id )
          deal2 = LookUp.create(name: 'deal-test-123', entity: 'deal_12345678', entity_id: '12345678', entity_type: 'deal', tenant_id: @user.tenant_id)
          email.related_to << deal1
          email.related_to << deal2
          email.save!
          email.reload
          FetchOutlookMessages.call(@new_payload)
          email = Email.find_by(source_id: 'message-890')
          expect(email.related_to.where(id: deal1.id)).not_to be_blank
          expect(email.related_to.where(id: deal2.id)).not_to be_blank
        end
      end
    end

    context 'when associated deal is present on contact' do
      before do
        @sample_message = @sample_message.with_indifferent_access
        from_params = @sample_message['from']
        @sample_message['from'] = @sample_message['ccRecipients'].first
        @sample_message['ccRecipients'] = [from_params]
        @user = create(:user, id: 12, tenant: create(:tenant, id: 14) )
        create(:connected_account, subscription_id: 'sub-123',  expires_at: (Time.now + 1.hour).to_i, user: @user)
        contact_search_response = { matched: [{ :entity=>"contact_1", :email=>"<EMAIL>", :name=>"Contact1 test", :tenant_id => @user.tenant_id, :associated_deals => [123] }],
                                    unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id },
                                                { entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        deal_search_response = [{ :entity => "deal_123", :name => 'Email Deal', :tenant_id => @user.tenant_id, :owner_id => 1212 }]
        lead_search_response = { matched: [{ :entity=>"lead_1", :email=>"<EMAIL>", :name=>"lead1 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        user_search_response = { matched: [{ :entity=>"user_12", :email=>"<EMAIL>", :name=>"user12 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [] }

        allow(SearchContacts).to receive_message_chain([:call, :result]).and_return(contact_search_response)
        allow(SearchDeals).to receive_message_chain([:call, :result]).and_return(deal_search_response)
        allow(SearchLeads).to receive_message_chain([:call, :result]).and_return(lead_search_response)
        allow(SearchUsers).to receive_message_chain([:call, :result]).and_return(user_search_response)

        allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-123').and_return(@sample_message)
        stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-123/attachments").
          to_return(status: 200, body: @sample_attachments.to_json, headers: {})
        allow(UploadFileToS3).to receive(:call).and_return(nil)

        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailReceivedRelatedToEntity))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2))
      end

      it 'saves message correctly' do
        expect(Email.count).to be_zero
        FetchOutlookMessages.call(@payload)
        email = Email.first

        expect(email.related_to.map(&:entity)).to match_array(["lead_1", "contact_1", "deal_123"])
      end
    end

    context 'when associated deal is present on multiple contacts' do
      before do
        @sample_message = @sample_message.with_indifferent_access
        from_params = @sample_message['from']
        @sample_message['from'] = @sample_message['ccRecipients'].first
        @sample_message['ccRecipients'] = [from_params]
        @user = create(:user, id: 12, tenant: create(:tenant, id: 14) )
        create(:connected_account, subscription_id: 'sub-123',  expires_at: (Time.now + 1.hour).to_i, user: @user)
        contact_search_response = { matched: [{ :entity=>"contact_1", :email=>"<EMAIL>", :name=>"Contact1 test", :tenant_id => @user.tenant_id, :associated_deals => [123] }, { :entity=>"contact_2", :email=>"<EMAIL>", :name=>"Contact2 test", :tenant_id => @user.tenant_id, :associated_deals => [123]}],
                                    unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id },
                                                { entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        deal_search_response = [{ :entity => "deal_123", :name => 'Email Deal', :tenant_id => @user.tenant_id, :owner_id => 1212 }]
        lead_search_response = { matched: [{ :entity=>"lead_1", :email=>"<EMAIL>", :name=>"lead1 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        user_search_response = { matched: [{ :entity=>"user_12", :email=>"<EMAIL>", :name=>"user12 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [] }

        allow(SearchContacts).to receive_message_chain([:call, :result]).and_return(contact_search_response)
        allow(SearchDeals).to receive_message_chain([:call, :result]).and_return(deal_search_response)
        allow(SearchLeads).to receive_message_chain([:call, :result]).and_return(lead_search_response)
        allow(SearchUsers).to receive_message_chain([:call, :result]).and_return(user_search_response)

        allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-123').and_return(@sample_message)
        stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-123/attachments").
          to_return(status: 200, body: @sample_attachments.to_json, headers: {})
        allow(UploadFileToS3).to receive(:call).and_return(nil)

        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailReceivedRelatedToEntity))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2))
      end

      it 'saves message correctly' do
        expect(Email.count).to be_zero
        FetchOutlookMessages.call(@payload)
        email = Email.first

        expect(email.related_to.map(&:entity)).to match_array(["lead_1", "contact_1", "contact_2", "deal_123"])
      end
    end

    context 'when associated deal is present on contact and existing lookup' do
      before do
        @sample_message = @sample_message.with_indifferent_access
        from_params = @sample_message['from']
        @sample_message['from'] = @sample_message['ccRecipients'].first
        @sample_message['ccRecipients'] = [from_params]
        @user = create(:user, id: 12, tenant: create(:tenant, id: 14) )
        create(:connected_account, subscription_id: 'sub-123',  expires_at: (Time.now + 1.hour).to_i, user: @user, tenant_id: @user.tenant_id)
        create(:look_up, entity: 'contact_1', email: '<EMAIL>', name: 'Contact1 test', tenant_id: @user.tenant_id)
        contact_search_response = { matched: [],
                                    unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id },
                                                { entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        deal_search_response = [{ :entity => "deal_123", :name => 'Email Deal', :tenant_id => @user.tenant_id, :owner_id => 1212 }]
        lead_search_response = { matched: [{ :entity=>"lead_1", :email=>"<EMAIL>", :name=>"lead1 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [{ entity: LOOKUP_CUSTOM_EMAIL, email: '<EMAIL>', tenant_id: @user.tenant_id }] }
        user_search_response = { matched: [{ :entity=>"user_12", :email=>"<EMAIL>", :name=>"user12 test", :tenant_id => @user.tenant_id }],
                                 unmatched: [] }
        contact_search_by_id_response = [{ :entity=>"contact_1", :email=>"<EMAIL>", :name=>"Contact1 test", :tenant_id => @user.tenant_id, :associated_deals => [123] }]

        allow(SearchContacts).to receive_message_chain([:call, :result]).and_return(contact_search_response)
        allow(SearchContactsByIds).to receive_message_chain([:call, :result]).and_return(contact_search_by_id_response)
        allow(SearchDeals).to receive_message_chain([:call, :result]).and_return(deal_search_response)
        allow(SearchLeads).to receive_message_chain([:call, :result]).and_return(lead_search_response)
        allow(SearchUsers).to receive_message_chain([:call, :result]).and_return(user_search_response)

        allow_any_instance_of(FetchOutlookMessages).to receive(:get_message).with('message-123').and_return(@sample_message)
        stub_request(:get, "#{GRAPH_HOST}/v1.0/me/messages/message-123/attachments").
          to_return(status: 200, body: @sample_attachments.to_json, headers: {})
        allow(UploadFileToS3).to receive(:call).and_return(nil)

        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailReceivedRelatedToEntity))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailAction))
        expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailCreatedWorkflowV2))
      end

      it 'saves message correctly' do
        expect(Email.count).to be_zero
        FetchOutlookMessages.call(@payload)
        email = Email.first

        expect(email.related_to.map(&:entity)).to match_array(["lead_1", "contact_1", "deal_123"])
      end

      it 'saves entities on email thread' do
         FetchOutlookMessages.call(@payload)
         email_thread = Email.first.email_thread
         expect(email_thread.contact_ids).not_to be_blank
         expect(email_thread.lead_ids).not_to be_blank
      end
    end
  end
end
