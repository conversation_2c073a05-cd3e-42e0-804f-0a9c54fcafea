# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ProcessImapMessage, type: :service do
  describe '#call' do
    let!(:connected_account) { create(:connected_account, provider_name: CUSTOM_PROVIDER, access_token: '05434a8d-806d-4f29-a642-35201ee588e8') }
    let(:email) { create(:email, connected_account: connected_account, tenant_id: connected_account.tenant_id, source_id: '<<EMAIL>>') }
    let(:message_failed_event) do
      {
        "serviceUrl" => "http://localhost:8080",
        "account" => "05434a8d-806d-4f29-a642-35201ee588e8",
        "date" => "2025-08-28T11:40:04.839Z",
        "event" => "messageFailed",
        "data" => {
          "messageId" => "<<EMAIL>>",
          "queueId" => "198f07a4fb4074eef5a",
          "error" => "Error: Invalid login: 535 Authentication Failed",
          "networkRouting" => nil
        }
      }
    end
    let(:message_new_plain_event) do
      JSON(file_fixture('imap_sent_message_only_plain_content.json').read)
    end

    let(:token) { FactoryBot.build(:auth_token, user_id: connected_account.user_id, tenant_id: connected_account.tenant_id).token }
    let(:auth_data) { ParseToken.call(token).result }

    before do
      thread = Thread.current
      thread[:auth] = auth_data
      thread[:token] = token
    end

    context 'when event is messageFailed' do
      before do
        email.update!(direction: 'sent')
      end

      it 'updates status and failed_reason of original email' do
        ProcessImapMessage.new(message_failed_event).call()

        expect(email.reload.status).to eq('failed')
        expect(email.failed_reason).to eq('Error: Invalid login: 535 Authentication Failed')
      end
    end

    context 'when event is messageNew' do
      before do
        data = {
          "content":[
            {
              "emails":[{"type":"OFFICE","value":"<EMAIL>","primary":true}],
              "lastName":"test","firstName":"Contact1","id":1, recordActions: { email: true }, ownerId: 123
            }
          ]
        }
        stub_request(:post, SERVICE_SEARCH + "/v1/search/contact?sort=updatedAt,desc&page=0&size=100").
          with(
            body: {
              fields: ["id", "firstName", "lastName", "emails", "associatedDeals", "ownerId"],
              jsonRule: {
                rules: [{
                  "operator": "in",
                  "id": "emails",
                  "field": "emails",
                  "type": "string",
                  "value": "<EMAIL>"
                }], "condition": "OR", "valid": true
              }
            }.to_json,
            headers: {
              "Authorization" => "Bearer #{ token }"
            }).
            to_return(status: 200, body: data.to_json, headers: {})
            LookUp.new(entity_id: 123, email: '<EMAIL>', entity_type: LOOKUP_USER, tenant_id: connected_account.tenant_id, name: 'User').save!
      end

      context 'when email html content is not present' do
        it 'saves plain content in email body' do
          ProcessImapMessage.new(message_new_plain_event).call()
          saved_mail = Email.find_by(global_message_id: '<<EMAIL>>')
          expect(saved_mail.status).to eq('received')
          expect(saved_mail.body).to eq("Dear Postal Systems,\n\nI hope this email finds you well...\n")
        end
      end
    end
  end
end