# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BounceProcessor, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:connected_account) { create(:connected_account, user: user, tenant_id: tenant.id, email: '<EMAIL>') }
  let(:outlook_connected_account) { create(:connected_account, user: user, tenant_id: tenant.id, email: '<EMAIL>', provider_name: MICROSOFT_PROVIDER) }
  let(:smtp_connected_account) { create(:connected_account, user: user, tenant_id: tenant.id, email: '<EMAIL>', provider_name: CUSTOM_PROVIDER) }

  let(:gmail_bounce_data) do
    data = JSON.parse(File.read(Rails.root.join('spec/fixtures/files/gmail_delivery_status_email.json')))
    data = data.deep_transform_keys { |k| k.to_s.underscore.to_sym }

    RecursiveOpenStruct.new(data)
  end
  
  let(:original_email) do
    create(:email,
      global_message_id: '<<EMAIL>>',
      connected_account: connected_account,
      tenant_id: tenant.id,
      status: :sent,
      owner: user
    )
  end

  let(:outlook_bounce_message) do
    JSON.parse(File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_email.json')))
  end

  let(:outlook_raw_mime) do
    File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_raw_mime.txt'))
  end

  let(:original_outlook_mail) do
    create(:email,
      global_message_id: '<<EMAIL>>',
      connected_account: outlook_connected_account,
      tenant_id: tenant.id,
      status: :sent,
      owner: user
    )
  end

  let(:smtp_original_mail) do
    create(:email,
      global_message_id: '<<EMAIL>>',
      connected_account: smtp_connected_account,
      tenant_id: tenant.id,
      status: :sent,
      owner: user
    )
  end

  let(:smtp_bounce_event) do
    JSON.parse(File.read(Rails.root.join('spec/fixtures/files/smtp_message_bounce_event.json')))
  end

  describe '#call' do
    context 'for gmail' do
      before do
        original_email
        original_email.update!(direction: 'sent')
      end

      subject { BounceProcessor.call(gmail_bounce_data, connected_account) }
  
      context 'with valid bounce email' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailFailed))
          expect(PublishEvent).to receive(:call).with(instance_of(Event::EmailUpdatedV2))
        end

        it 'updates the original email with bounce information' do
          subject
          original_email.reload
          
          expect(original_email.bounce_type).to eq('hard')
          expect(original_email.failed_reason).to eq('550-5.1.1 The email account that you tried to reach does not exist. Please try')
          expect(original_email.status).to eq('failed')
        end
      end

      context 'when original email is not found' do
        before do
          original_email.update!(global_message_id: 'different-message-id')
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailFailed))
        end

        it 'logs a warning' do
          expect(Rails.logger).to receive(:warn).with(/Could not find original email/)
          subject
        end
      end

      context 'with non-bounce email' do
        let(:regular_gmail_data) do
          gmail_bounce_data.deep_dup.tap do |data|
            data['payload']['headers'].find { |h| h['name'] == 'Subject' }['value'] = 'Regular email'
            data['payload']['headers'].find { |h| h['name'] == 'From' }['value'] = '<EMAIL>'
          end
        end

        subject { BounceProcessor.call(regular_gmail_data, connected_account) }

        it 'does not process as bounce' do
          subject
          original_email.reload

          expect(original_email.bounce_type).to be_nil
          expect(original_email.failed_reason).to be_nil
        end
      end
    end

    context 'for outlook' do
      before do
        original_outlook_mail
        original_outlook_mail.update!(direction: 'sent')

        stub_request(:get, "https://graph.microsoft.com/v1.0/me/messages/#{outlook_bounce_message['id']}/$value").to_return(status: 200, body: outlook_raw_mime, headers: {})
      end

      subject { BounceProcessor.call(outlook_bounce_message, outlook_connected_account) }
  
      context 'with valid bounce email' do
        before do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailFailed), {})
        end

        it 'updates the original email with bounce information' do
          subject
          original_outlook_mail.reload
          
          expect(original_outlook_mail.bounce_type).to eq('hard')
          expect(original_outlook_mail.failed_reason).to eq('smtp;550-5.1.1 The email account that you tried to reach does not exist. Please try')
          expect(original_outlook_mail.status).to eq('failed')
        end
      end

      context 'when original email is not found' do
        before do
          original_outlook_mail.update!(global_message_id: 'different-message-id')
        end

        it 'logs a warning' do
          expect(Rails.logger).to receive(:warn).with(/Could not find original email/)
          subject
        end
      end

      context 'with non-bounce email' do
        let(:regular_outlook_message) do
          {
            'id' => 'regular123',
            'subject' => 'Regular email',
            'from' => {
              'emailAddress' => {
                'address' => '<EMAIL>'
              }
            },
            'body' => {
              'content' => 'This is a regular email message.'
            }
          }
        end

        subject { BounceProcessor.call(regular_outlook_message, outlook_connected_account) }

        it 'does not process as bounce' do
          subject
          original_outlook_mail.reload

          expect(original_outlook_mail.bounce_type).to be_nil
          expect(original_outlook_mail.failed_reason).to be_nil
        end
      end
    end

    context 'for smtp mails' do
      before do
        smtp_original_mail
        smtp_original_mail.update!(direction: 'sent')
      end

      subject { BounceProcessor.call(smtp_bounce_event, smtp_connected_account) }
  
      context 'with valid bounce email' do
        before do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::EmailFailed), {})
        end

        it 'updates the original email with bounce information' do
          subject
          smtp_original_mail.reload
          
          expect(smtp_original_mail.bounce_type).to eq('hard')
          expect(smtp_original_mail.failed_reason).to eq("5.1.1 The email account that you tried to reach does not exist. Please try 5.1.1 double-checking the recipient's email address for typos or 5.1.1 unnecessary spaces. For more information, go to 5.1.1  https://support.google.com/mail/?p=NoSuchUser d9443c01a7336-2466879d6bdsi98977525ad.58 - gsmtp")
          expect(smtp_original_mail.status).to eq('failed')
        end
      end

      context 'when original email is not found' do
        before do
          smtp_original_mail.update!(global_message_id: 'different-message-id')
        end

        it 'logs a warning' do
          expect(Rails.logger).to receive(:warn).with(/Could not find original email/)
          subject
        end
      end
    end
  end
end
