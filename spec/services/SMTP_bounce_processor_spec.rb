# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SMTPBounceProcessor, type: :service do
  describe '#call' do
    context 'with valid hard bounce event' do
      let(:bounce_event) do
        JSON.parse(File.read(Rails.root.join('spec/fixtures/files/smtp_message_bounce_event.json')))
      end

      it 'should extract bounce info from event' do
        bounce_info = SMTPBounceProcessor.call(bounce_event)

        expect(bounce_info).to eq({
          is_bounced: true,
          bounce_type: 'hard',
          original_message_id: "<<EMAIL>>",
          failed_reason: "5.1.1 The email account that you tried to reach does not exist. Please try 5.1.1 double-checking the recipient's email address for typos or 5.1.1 unnecessary spaces. For more information, go to 5.1.1  https://support.google.com/mail/?p=NoSuchUser d9443c01a7336-2466879d6bdsi98977525ad.58 - gsmtp"
        })
      end
    end

    context 'with valid soft bounce event' do
      let(:bounce_event) do
        JSON.parse(File.read(Rails.root.join('spec/fixtures/files/smtp_message_soft_bounce_event.json')))
      end

      it 'should extract bounce info from event' do
        bounce_info = SMTPBounceProcessor.call(bounce_event)

        expect(bounce_info).to eq({
          is_bounced: true,
          bounce_type: 'soft',
          original_message_id: "<<EMAIL>>",
          failed_reason: "4.1.1 The email account that you tried to reach is temporarily unavailable. Please try again later."
        })
      end
    end
  end
end
