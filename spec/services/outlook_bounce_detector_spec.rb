# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OutlookBounceDetector, type: :service do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:connected_account) { create(:connected_account, user: user, tenant_id: tenant.id) }

  let(:outlook_bounce_message) do
    JSON.parse(File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_email.json')))
  end

  let(:outlook_raw_mime) do
    File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_raw_mime.txt'))
  end

  let(:outlook_raw_mime_soft_bounce) do
    File.read(Rails.root.join('spec/fixtures/files/outlook_delivery_status_soft_bounce_raw_mime.txt'))
  end

  let(:regular_outlook_message) do
    {
      'id' => 'regular123',
      'subject' => 'Regular email',
      'from' => {
        'emailAddress' => {
          'address' => '<EMAIL>'
        }
      },
      'body' => {
        'content' => 'This is a regular email message.'
      }
    }
  end

  before do
    stub_request(:get, "https://graph.microsoft.com/v1.0/me/messages/#{outlook_bounce_message['id']}/$value").to_return(status: 200, body: outlook_raw_mime, headers: {})
  end

  describe '.call' do
    context 'with an Outlook bounce message' do
      subject { OutlookBounceDetector.call(outlook_bounce_message, { token: 'test_token' }) }

      it 'detects the message as a bounce' do
        expect(subject[:is_bounced]).to be true
      end

      it 'determines the correct bounce type' do
        expect(subject[:bounce_type]).to eq('hard')
      end

      it 'extracts the failed reason' do
        expect(subject[:failed_reason]).to include('smtp;550-5.1.1 The email account that you tried to reach does not exist. Please try')
      end

      it 'extracts the original message ID from headers' do
        expect(subject[:original_message_id]).to eq('<<EMAIL>>')
      end
    end

    context 'with a regular Outlook message' do
      subject { OutlookBounceDetector.call(regular_outlook_message, { token: 'test_token' }) }

      it 'does not detect the message as a bounce' do
        expect(subject[:is_bounced]).to be false
      end
    end

    context 'with Microsoft Exchange sender and excluded subject' do
      let(:microsoft_exchange_delivered_message) do
        {
          'id' => 'exchange123',
          'subject' => 'Delivered: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Your message was delivered.'
          }
        }
      end

      subject { OutlookBounceDetector.call(microsoft_exchange_delivered_message, { token: 'test_token' }) }

      it 'does not detect as bounce' do
        expect(subject[:is_bounced]).to be false
      end
    end

    context 'with postmaster sender bounce' do
      let(:postmaster_bounce) do
        {
          'id' => 'postmaster123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Mail delivery failed.'
          }
        }
      end

      before do
        stub_request(:get, "https://graph.microsoft.com/v1.0/me/messages/#{postmaster_bounce['id']}/$value").to_return(status: 200, body: outlook_raw_mime, headers: {})
      end

      subject { OutlookBounceDetector.call(postmaster_bounce, { token: 'test_token' }) }

      it 'detects as bounce from postmaster' do
        expect(subject[:is_bounced]).to be true
        expect(subject[:bounce_type]).to eq 'hard'
        expect(subject[:failed_reason]).to eq 'smtp;550-5.1.1 The email account that you tried to reach does not exist. Please try'
      end
    end

    context 'with postmaster sender and excluded subject' do
      let(:postmaster_relayed_message) do
        {
          'id' => 'postmaster123',
          'subject' => 'Relayed: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Mail was submitted.'
          }
        }
      end

      subject { OutlookBounceDetector.call(postmaster_relayed_message, { token: 'test_token' }) }

      it 'does not detect as bounce' do
        expect(subject[:is_bounced]).to be false
      end
    end

    context 'with soft bounce indicators' do
      let(:soft_bounce_message) do
        {
          'id' => 'soft123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => '421 4.2.1 Mailbox temporarily unavailable. Try again later.'
          }
        }
      end

      before do
        allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(outlook_raw_mime_soft_bounce)
      end

      subject { OutlookBounceDetector.call(soft_bounce_message, { token: 'test-token' }) }

      it 'detects as soft bounce' do
        expect(subject[:is_bounced]).to be true
        expect(subject[:bounce_type]).to eq('soft')
      end
    end

    context 'when raw MIME verification fails' do
      let(:bounce_message_no_dsn) do
        {
          'id' => 'no_dsn123',
          'subject' => 'Undeliverable: Test Message',
          'from' => {
            'emailAddress' => {
              'address' => '<EMAIL>'
            }
          },
          'body' => {
            'content' => 'Your message could not be delivered.'
          }
        }
      end

      before do
        raw_mime_no_dsn = "From: <EMAIL>\nTo: <EMAIL>\nSubject: Test\n\nTest body"
        allow_any_instance_of(OutlookBounceDetector).to receive(:get_raw_mime_message).and_return(raw_mime_no_dsn)
      end

      subject { OutlookBounceDetector.call(bounce_message_no_dsn, { token: 'test-token' }) }

      it 'returns false when DSN verification fails' do
        expect(subject[:is_bounced]).to be false
      end
    end
  end
end
