# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ListenForImapWebhook, type: :service do
  describe '#call' do
    let(:connected_account) { create(:connected_account, provider_name: CUSTOM_PROVIDER, access_token: '05434a8d-806d-4f29-a642-35201ee588e8') }
    let(:smtp_message) { create(:email, connected_account: connected_account, global_message_id: '<<EMAIL>>') }

    before do
      payload = {
        'params' => JSON.parse(File.read(Rails.root.join('spec/fixtures/files/smtp_message_bounce_event.json')))
      }.to_json
      smtp_message.update!(direction: 'sent')

      allow(RabbitmqConnection).to receive(:subscribe)
        .with(EMAIL_EXCHANGE, IMAP_WEBHOOK_EVENT, IMAP_WEBHOOK_QUEUE)
        .and_yield(payload.to_s) 
    end

    context 'when bounce event is received' do
      it 'processes the bounce event' do
        ListenForImapWebhook.call

        smtp_message.reload

        expect(smtp_message.bounce_type).to eq('hard')
      end
    end
  end
end