require 'rails_helper'

RSpec.describe FilterEmailsQuery do
  let!(:user) { create(:user)}
  let!(:another_user) { create(:user, tenant_id: user.tenant_id)}
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:valid_auth_data) { ParseToken.call(valid_auth_token.token).result }
  let!(:headers) { valid_headers(valid_auth_token) }

  before do
    allow_any_instance_of(EmailLookUp).to receive(:publish_entity_metadata)
    Thread.current[:auth] = valid_auth_data
    Thread.current[:token] = valid_auth_token

    [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
      stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
        .with(
          headers: {
            Authorization: "Bearer #{valid_auth_token}"
          }
        )
        .to_return(status: 200, body: { accessByOwners: { "4167" => { email: true } }, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
    end
  end

  describe '#call' do
    context 'query' do
      let(:no_filter_params) { ActionController::Parameters.new({}).permit! }

      context 'when user has read all' do
        let(:valid_rule)    { { 'type': 'related_lookup', 'field': 'related_to', 'operator': 'equal', 'value': { 'id': @to_lookup.entity_id, 'entity': @to_lookup.entity_type }} }
        let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 10, jsonRule: { "condition": "AND", "rules": [valid_rule] }} ).permit! }

        before do
          thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
          @emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: thread.id)
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @emails.each { |e| e.related_to << @to_lookup; e.email_thread.update(lead_ids: [@to_lookup.entity_id]) }

          another_thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
          other_emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: another_thread.id)
          @lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          other_emails.each { |e| e.related_to << @lead_lookup }

          @deleted_email = create(:email, deleted: true, tenant_id: user.tenant_id)
          allow_any_instance_of(Auth::Data).to receive(:can_read_all_emails?).and_return(true)
        end

        it 'returns all emails for the tenant' do
          emails = FilterEmailsQuery.call(valid_auth_data, no_filter_params).result

          expect(emails.size).to eq(2)
          expect(Email.where(id: emails.collect(&:id)).map(&:email_thread_id)).to match_array([@emails.first.email_thread_id, @emails.first.email_thread_id + 1])
        end

        it 'does not return deleted email' do
        emails = FilterEmailsQuery.call(valid_auth_data, no_filter_params).result

        expect(emails.pluck(:id)).not_to include @deleted_email.id

        end
      end

      context 'when user has read' do
        let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 10, jsonRule: { "condition": "AND", "rules": [valid_rule] }} ).permit! }

        before do
          thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
          @emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: thread.id)
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: 4167)
          @emails.each { |e| e.related_to << @to_lookup; e.email_thread.update(lead_ids: [@to_lookup.entity_id]) }


          another_thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
          other_emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: another_thread.id)
          @lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: 4167)
          other_emails.each { |e| e.related_to << @lead_lookup }
        end

        context 'when related lookup filter is applied' do
          let(:valid_rule)    { { 'type': 'related_lookup', 'field': 'related_to', 'operator': 'equal', 'value': { 'id': @to_lookup.entity_id, 'entity': @to_lookup.entity_type }} }

          context 'when entity is shared' do
            it 'returns command success' do
              command = FilterEmailsQuery.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
            end

            it 'returns one email of thread' do
              emails = FilterEmailsQuery.call(valid_auth_data, filter_params).result
              emails = Email.where(id: emails.collect(&:id))
              expect(emails.size).to eq(1)
              expect(emails.first.email_thread_id).to eq(@emails.first.email_thread_id)
              expect(emails.first.id).to eq(@emails.last.id)
            end
          end

          context 'when user is participant' do
            before do
              @to_lookup.update(owner_id: 4168)
              @lead_lookup.update(owner_id: 4168)
              user_lookup = build(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id)
              @emails[1].to_recipients << user_lookup
              @emails[1].email_thread.update(user_ids: [user_lookup.entity_id], lead_ids: [@to_lookup.entity_id])
            end

            it 'returns one email of thread' do
              emails = FilterEmailsQuery.call(valid_auth_data, filter_params).result

              emails = Email.where(id: emails.collect(&:id))
              expect(emails.size).to eq(1)
              expect(emails.first.email_thread_id).to eq(@emails.first.email_thread_id)
              expect(emails.first.id).to eq(@emails[1].id)
            end
          end
        end

        context 'when related lookup filter is not applied' do
          before do
            some_thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
            some_emails = create_list(:email, 3, owner: another_user, tenant_id: user.tenant_id, email_thread_id: some_thread.id)
            some_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
            some_emails.each { |e| e.related_to << some_lookup }
          end

          it 'returns one email from all threads accessible' do
            emails = FilterEmailsQuery.call(valid_auth_data, no_filter_params).result

            emails = Email.where(id: emails.collect(&:id))
            expect(emails.size).to eq(2)
            expect(emails.map(&:email_thread_id)).to match_array([@emails.first.email_thread_id, @emails.first.email_thread_id + 1])
          end
        end

        context 'when multi field (search) is applied' do
          let(:valid_rule)    {
            {
              id: 'multi_field',
              type: 'multi_field',
              field: 'multi_field',
              operator: 'multi_field',
              value: 'random'
            }
          }

          context 'when no email matches' do
            it 'returns empty array' do
              command = described_class.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
              expect(command.result). to eq([])
            end
          end

          context 'when search string matches a substring in to emails' do
            before do
              @emails.first.update(to: ['<EMAIL>', '<EMAIL>'])
            end

            it 'returns email' do
              command = described_class.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
              expect(command.result.count).to eq(1)
              expect(command.result.first.id).to eq(@emails.first.id)
            end
          end

          context 'when search string matches a substring in cc emails' do
            before do
              @emails.first.update(cc: ['<EMAIL>', '<EMAIL>'])
            end

            it 'returns email' do
              command = described_class.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
              expect(command.result.count).to eq(1)
              expect(command.result.first.id).to eq(@emails.first.id)
            end
          end

          context 'when search string matches a substring in bcc emails' do
            before do
              @emails.first.update(bcc: ['<EMAIL>', '<EMAIL>'])
            end

            it 'returns email' do
              command = described_class.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
              expect(command.result.count).to eq(1)
              expect(command.result.first.id).to eq(@emails.first.id)
            end
          end

          context 'when search string matches a substring in from email' do
            before do
              @emails.first.update(from: '<EMAIL>')
            end

            it 'returns email' do
              command = described_class.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
              expect(command.result.count).to eq(1)
              expect(command.result.first.id).to eq(@emails.first.id)
            end
          end

          context 'when search string matches a substring in subject' do
            before do
              @emails.first.update(subject: 'ThIs Is RaNDOm subjecT.')
            end

            it 'returns email' do
              command = described_class.call(valid_auth_data, filter_params)

              expect(command.success?).to be_truthy
              expect(command.result.count).to eq(1)
              expect(command.result.first.id).to eq(@emails.first.id)
            end
          end
        end
      end
    end

    context 'filters' do
      let(:another_user_lookup) { build(:look_up, entity_type: LOOKUP_USER, entity_id: another_user.id, tenant_id: user.tenant_id) }
      let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 10, jsonRule: { "condition": "AND", "rules": valid_rules }} ) }
      let(:datetime_value) { DateTime.now.utc.iso8601 }

      context 'when equal operator is applied' do
        let(:valid_rules) {
          [
            { type: 'long', id: 'id', field: 'id', operator: 'equal', value: @email.email_thread_id },
            { type: 'string', id: 'subject', field: 'subject', operator: 'equal', value: 'Email Subject' },
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'equal', value: datetime_value },
            { type: 'long', id: 'sentBy', field: 'sentBy', operator: 'equal', value: @email.sender.entity_id },
            { type: 'long', id: 'receivedBy', field: 'receivedBy', operator: 'equal', value: another_user.id },
            { type: 'long', id: 'associatedLeads', field: 'associatedLeads', operator: 'equal', value: @lead_lookup.entity_id },
            { type: 'long', id: 'associatedContacts', field: 'associatedContacts', operator: 'equal', value: @contact_lookup.entity_id },
            { type: 'long', id: 'associatedDeals', field: 'associatedDeals', operator: 'equal', value: @deal_lookup.entity_id },
            { type: 'string', id: 'read', field: 'read', operator: 'equal', value: true },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'equal', value: datetime_value },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'equal', value: datetime_value }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject', created_at: datetime_value, read_by: [user.id])
          @lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @contact_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
          @deal_lookup = build(:look_up, entity_type: LOOKUP_DEAL, tenant_id: user.tenant_id)
          @email.related_to << @deal_lookup
          @email.to_recipients << @lead_lookup
          @email.cc_recipients << @contact_lookup
          @email.to_recipients << another_user_lookup
          @email.email_thread.update(lead_ids: [@lead_lookup.entity_id], contact_ids: [@contact_lookup.entity_id], user_ids: [another_user_lookup.entity_id], deal_ids: [@deal_lookup.entity_id])
          @email.sender.update(entity_id: user.id, entity_type: "user", entity: "user_#{user.id}")
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          opened_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'Opened Email', created_at: datetime_value, read_by: [user.id])
          opened_email.update(status: 'opened')
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end

        context 'when field is status' do
          let(:valid_rules) {
            [
              { type: 'string', id: 'status', field: 'status', operator: 'equal', value: 'Opened' }
            ]
          }

          it 'returns email having requested status' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result
            
            emails = Email.where(id: emails.collect(&:id))
            expect(emails.count).to eq(1)
            expect(emails[0].subject).to eq('Opened Email')
            expect(emails[0].status).to eq('opened')
          end
        end
      end

      context 'when not equal operator is applied' do
        let(:valid_rules) {
          [
            { type: 'long', id: 'id', field: 'id', operator: 'not_equal', value: @email.email_thread_id },
            { type: 'string', id: 'subject', field: 'subject', operator: 'not_equal', value: 'Email Subject' },
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'not_equal', value: datetime_value },
            { type: 'long', id: 'sentBy', field: 'sentBy', operator: 'not_equal', value: @email.sender.entity_id },
            { type: 'long', id: 'receivedBy', field: 'receivedBy', operator: 'not_equal', value: another_user.id },
            { type: 'long', id: 'associatedLeads', field: 'associatedLeads', operator: 'not_equal', value: @lead_lookup.entity_id },
            { type: 'long', id: 'associatedContacts', field: 'associatedContacts', operator: 'not_equal', value: @contact_lookup.entity_id },
            { type: 'long', id: 'associatedDeals', field: 'associatedDeals', operator: 'not_equal', value: @deal_lookup.entity_id },
            { type: 'string', id: 'read', field: 'read', operator: 'not_equal', value: 'true' },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'not_equal', value: datetime_value },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'not_equal', value: datetime_value }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject', created_at: datetime_value, read_by: [user.id])
          @lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @contact_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
          @deal_lookup = build(:look_up, entity_type: LOOKUP_DEAL, tenant_id: user.tenant_id)
          @email.related_to << @deal_lookup
          @email.to_recipients << @lead_lookup
          @email.cc_recipients << @contact_lookup
          @email.to_recipients << another_user_lookup
          @email.sender.update(entity: "user_#{user.id}")
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'Opened Email', created_at: datetime_value, read_by: [user.id], status: 'opened')
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(0)
        end

        context 'when field is status' do
          let(:valid_rules) {
            [
              { type: 'string', id: 'status', field: 'status', operator: 'not_equal', value: 'Opened' }
            ]
          }

          it 'returns email having requested status' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            emails = Email.where(id: emails.collect(&:id))
            expect(emails.count).to eq(1)
            expect(emails.pluck(:status)).to eq(['received'])
          end
        end
      end

      context 'when in operator is applied' do
        let(:valid_rules) {
          [
            { type: 'long', id: 'id', field: 'id', operator: 'in', value: [@email.email_thread_id, 1, 2] },
            { type: 'string', id: 'subject', field: 'subject', operator: 'in', value: 'Email Subject,not email subject' },
            { type: 'long', id: 'sentBy', field: 'sentBy', operator: 'in', value: [@email.sender.entity_id, 1, 2] },
            { type: 'long', id: 'receivedBy', field: 'receivedBy', operator: 'in', value: [another_user.id, 1, 2] },
            { type: 'long', id: 'associatedLeads', field: 'associatedLeads', operator: 'in', value: [@lead_lookup.entity_id, 1, 2] },
            { type: 'long', id: 'associatedContacts', field: 'associatedContacts', operator: 'in', value: [@contact_lookup.entity_id, 1, 2] },
            { type: 'long', id: 'associatedDeals', field: 'associatedDeals', operator: 'in', value: [@deal_lookup.entity_id, 1, 2] }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, entity_type: LOOKUP_LEAD)
          @contact_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id, entity_type: LOOKUP_CONTACT)
          @deal_lookup = build(:look_up, entity_type: LOOKUP_DEAL, tenant_id: user.tenant_id, entity_type: LOOKUP_DEAL)
          @email.related_to << @deal_lookup
          @email.to_recipients << @lead_lookup
          @email.cc_recipients << @contact_lookup
          @email.to_recipients << another_user_lookup
          @email.email_thread.update(lead_ids: [@lead_lookup.entity_id], contact_ids: [@contact_lookup.entity_id], deal_ids: [@deal_lookup.entity_id], user_ids: [another_user_lookup.entity_id])
          @email.sender.update(entity_id: user.id, entity_type: "user", entity: "user_#{user.id}")
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when not in operator is applied' do
        let(:valid_rules) {
          [
            { type: 'long', id: 'id', field: 'id', operator: 'not_in', value: [@email.email_thread_id, 1, 2] },
            { type: 'string', id: 'subject', field: 'subject', operator: 'not_in', value: 'Email Subject,not email subject' },
            { type: 'long', id: 'sentBy', field: 'sentBy', operator: 'not_in', value: [@email.sender.entity_id, 1001] },
            { type: 'long', id: 'receivedBy', field: 'receivedBy', operator: 'not_in', value: [another_user.id, 1, 2] },
            { type: 'long', id: 'associatedLeads', field: 'associatedLeads', operator: 'not_in', value: [@lead_lookup.entity_id, 1, 2] },
            { type: 'long', id: 'associatedContacts', field: 'associatedContacts', operator: 'not_in', value: [@contact_lookup.entity_id, 1, 2] },
            { type: 'long', id: 'associatedDeals', field: 'associatedDeals', operator: 'not_in', value: [@deal_lookup.entity_id, 1, 2] }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @lead_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @contact_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
          @deal_lookup = build(:look_up, entity_type: LOOKUP_DEAL, tenant_id: user.tenant_id)
          @email.related_to << @deal_lookup
          @email.to_recipients << @lead_lookup
          @email.cc_recipients << @contact_lookup
          @email.to_recipients << another_user_lookup
          @email.sender.update(entity: "user_#{user.id}")
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(0)
        end
      end

      context 'when is null, is empty operator is applied' do
        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: nil)
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          @email.sender.update(entity: "user_#{user.id}")
          @email.email_thread.update(lead_ids: [@to_lookup.entity_id])
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id)
        end

        context 'when string type field' do
          %w[is_null is_empty].each do |operator|
            let(:valid_rules) {
              [
                { type: 'string', id: 'subject', field: 'subject', operator: operator, value: nil }
              ]
            }

            it 'returns email thread with matching email' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end
          end
        end

        context 'when date type field' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'is_null', value: nil }
            ]
          }

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(0)
          end
        end

        context 'when opened at, clicked at fields' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'is_null', value: nil },
              { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'is_null', value: nil }
            ]
          }

          before do
            thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @another_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: nil)
            @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
            @another_email.related_to << @to_lookup
            @another_email.sender.update(entity: "user_#{user.id}")
          end

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
            expect(emails.first.id).to eq(@another_email.id)
          end
        end

        context 'when long type field' do
          let(:valid_rules) {
            [
              { type: 'long', id: 'id', field: 'id', operator: 'is_null', value: nil },
              { type: 'long', id: 'sentBy', field: 'sentBy', operator: 'is_null', value: nil }
            ]
          }

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(0)
          end
        end

        context 'when associated entity' do
          [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity_type|
            let(:valid_rules) {
              [
                { type: 'long', id: "associated#{entity_type.titleize.pluralize}", field: "associated#{entity_type.titleize.pluralize}", operator: 'in', value: 1 }
              ]
            }

            before do
              @to_lookup.update!(entity_id: 10002, entity_type: entity_type)
              Email.all.each do |e|
                e.email_thread.update("#{entity_type}_ids": [10002])
              end
            end

            it 'returns email thread with matching email' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(0)
            end
          end
        end

        context 'when attachment filter' do
          let(:valid_rules) {
            [{ type: 'string', id: 'attachments', field: 'attachments', operator: 'is_null', value: nil }]
          }

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
          end
        end
      end

      context 'when is not null, is not empty operator is applied' do
        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          @email.sender.update(entity: "user_#{user.id}")
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id)
        end

        context 'when string type field' do
          %w[is_not_null is_not_empty].each do |operator|
            let(:valid_rules) {
              [
                { type: 'long', id: 'id', field: 'id', operator: 'is_not_null', value: nil },
                { type: 'string', id: 'subject', field: 'subject', operator: operator, value: nil }
              ]
            }

            it 'returns email thread with matching email' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end
          end
        end

        context 'when date type field' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'is_not_null', value: nil },
              { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'is_not_null', value: nil },
              { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'is_not_null', value: nil }
            ]
          }

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
            expect(emails.first.id).to eq(@email.id)
          end
        end

        context 'when long type field' do
          let(:valid_rules) {
            [
              { type: 'long', id: 'sentBy', field: 'sentBy', operator: 'is_not_null', value: nil }
            ]
          }

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
            expect(emails.first.id).to eq(@email.id)
          end
        end

        context 'when associated entity' do
          [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity_type|
            let(:valid_rules) {
              [
                { type: 'long', id: "associated#{entity_type.titleize.pluralize}", field: "associated#{entity_type.titleize.pluralize}", operator: 'is_not_null', value: nil }
              ]
            }

            before do
              @to_lookup.update(entity_id: 10002, entity_type: entity_type)
            end

            it 'returns email thread with matching email' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end
          end
        end

        context 'when attachment filter' do
          let(:valid_rules) {
            [{ type: 'string', id: 'attachments', field: 'attachments', operator: 'is_not_null', value: nil }]
          }

          before do
            create(:attachment, inline: false, email: @email)
            email_thread_attachments_count = Attachment.select(:id).where(inline: false).joins(:email).where(emails: { tenant_id: user.tenant_id }).group('attachments.email_id').count
            email_thread_attachments_count.each do |email_id, attachment_count|
              Email.where(id: email_id).update_all(external_attachment_count: attachment_count)
            end
          end

          it 'returns email thread with matching email' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
          end
        end
      end

      context 'when begins with operator is applied' do
        let(:valid_rules) {
          [
            { type: 'string', id: 'subject', field: 'subject', operator: 'begins_with', value: 'Email' }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when contains operator is applied' do
        let(:valid_rules) {
          [
            { type: 'string', id: 'subject', field: 'subject', operator: 'contains', value: 'ail sub' }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when not contains operator is applied' do
        let(:valid_rules) {
          [
            { type: 'string', id: 'subject', field: 'subject', operator: 'not_contains', value: 'emailsub' }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when greater operator is applied' do
        let(:one_hour_ago_time) { (DateTime.now - 1.hour).utc }
        let(:one_hour_ago) { one_hour_ago_time.iso8601 }
        let(:one_hour_ago_integer) { one_hour_ago_time.to_i * 1000 }
        let(:valid_rules) {
          [
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'greater', value: one_hour_ago_integer },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'greater', value: one_hour_ago },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'greater', value: one_hour_ago }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id)
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when less operator is applied' do
        let(:one_hour_later_time) { (DateTime.now + 1.hour).utc }
        let(:one_hour_later) { one_hour_later_time.iso8601 }
        let(:one_hour_later_integer) { one_hour_later_time.to_i * 1000 }
        let(:valid_rules) {
          [
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'less', value: one_hour_later_integer },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'less', value: one_hour_later },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'less', value: one_hour_later }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id)
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when greater or equal operator is applied' do
        let(:valid_rules) {
          [
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'greater_or_equal', value: datetime_value },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'greater_or_equal', value: datetime_value },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'greater_or_equal', value: datetime_value }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id, created_at: (DateTime.now + 5.minutes).utc.iso8601)
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when less or equal operator is applied' do
        let(:valid_rules) {
          [
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'less_or_equal', value: datetime_value },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'less_or_equal', value: datetime_value },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'less_or_equal', value: datetime_value }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject', created_at: datetime_value)
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id, created_at: (DateTime.now - 5.minutes).utc.iso8601)
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when between operator is applied' do
        let(:five_minutes_ago) { (DateTime.now - 5.minutes).utc.iso8601 }
        let(:valid_rules) {
          [
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'between', value: [five_minutes_ago, datetime_value] },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'between', value: [five_minutes_ago, datetime_value] },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'between', value: [five_minutes_ago, datetime_value] }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject', created_at: datetime_value)
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id, created_at: datetime_value)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id, created_at: (DateTime.now - 2.minutes).utc.iso8601)
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when not between operator is applied' do
        let(:five_minutes_ago) { (DateTime.now - 5.minutes).utc.iso8601 }
        let(:valid_rules) {
          [
            { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'not_between', value: [five_minutes_ago, datetime_value] },
            { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'not_between', value: [five_minutes_ago, datetime_value] },
            { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'not_between', value: [five_minutes_ago, datetime_value] }
          ]
        }

        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
          @email.related_to << @to_lookup
          create(:email_track_log, email: @email, tenant_id: @email.tenant_id)
          create(:email_link_log, email: @email, tenant_id: @email.tenant_id)
        end

        it 'returns email thread with matching email' do
          emails = described_class.call(valid_auth_data, filter_params.permit!).result

          expect(emails.size).to eq(1)
          expect(emails.first.id).to eq(@email.id)
        end
      end

      context 'when read and attachments filter is applied simultaneously' do
        before do
          thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
          @another_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'another email subject')
          @another_email.related_to << build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)

          @email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, subject: 'email subject')
          @email.related_to << build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
        end

        context 'when read equal true', desc: 'all emails in thread must be read' do
          before do
            @email.update_column(:read_by, [user.id])
            @another_email.update_column(:read_by, [user.id])
          end

          context 'when attachment is set' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'equal', value: true },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_not_null', value: nil }
              ]
            }

            before { @email.update_column(:external_attachment_count, 1) }

            it 'returns email read by current user and has attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end

            context 'when another email in thread remains unread' do
              before { @another_email.update_column(:read_by, []) }

              it 'returns 0 emails' do
                emails = described_class.call(valid_auth_data, filter_params.permit!).result
                expect(emails.size).to eq(0)
              end
            end
          end

          context 'when attachment is not set', desc: 'none of the emails in thread should have attachment' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'equal', value: true },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_null', value: nil }
              ]
            }

            it 'returns email read by current user and does not have attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end

            context 'when another email in thread has an attachment' do
              before { @another_email.update_column(:external_attachment_count, 1) }

              it 'returns 0 emails' do
                emails = described_class.call(valid_auth_data, filter_params.permit!).result
                expect(emails.size).to eq(0)
              end
            end
          end
        end

        context 'when read equal false', desc: 'matching email should always be unread' do
          context 'when attachment is set' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'equal', value: false },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_not_null', value: nil }
              ]
            }

            before { @email.update_column(:external_attachment_count, 1) }

            it 'returns email not read by current user and has attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end
          end

          context 'when attachment is not set', desc: 'none of the emails in thread should have attachment' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'equal', value: 'false' },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_null', value: nil }
              ]
            }

            it 'returns email not read by current user and does not have attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end

            context 'when another email in thread has an attachment' do
              before { @another_email.update_column(:external_attachment_count, 1) }

              it 'returns 0 emails' do
                emails = described_class.call(valid_auth_data, filter_params.permit!).result
                expect(emails.size).to eq(0)
              end
            end
          end
        end

        context 'when read not equal true', desc: 'matching email should always be unread' do
          context 'when attachment is set' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'not_equal', value: true },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_not_null', value: nil }
              ]
            }

            before { @email.update_column(:external_attachment_count, 1) }

            it 'returns email not read by current user and has attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end
          end

          context 'when attachment is not set', desc: 'none of the emails in thread should have attachment' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'not_equal', value: 'true' },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_null', value: nil }
              ]
            }

            it 'returns email not read by current user and does not have attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end

            context 'when another email in thread has an attachment' do
              before { @another_email.update_column(:external_attachment_count, 1) }

              it 'returns 0 emails' do
                emails = described_class.call(valid_auth_data, filter_params.permit!).result
                expect(emails.size).to eq(0)
              end
            end
          end
        end

        context 'when read not equal false', desc: 'all emails in thread must be read' do
          before do
            @email.update_column(:read_by, [user.id])
            @another_email.update_column(:read_by, [user.id])
          end

          context 'when attachment is set' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'not_equal', value: false },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_not_null', value: nil }
              ]
            }

            before { @email.update_column(:external_attachment_count, 1) }

            it 'returns email read by current user and has attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end

            context 'when another email in thread remains unread' do
              before { @another_email.update_column(:read_by, []) }

              it 'returns 0 emails' do
                emails = described_class.call(valid_auth_data, filter_params.permit!).result
                expect(emails.size).to eq(0)
              end
            end
          end

          context 'when attachment is not set', desc: 'none of the emails in thread should have attachment' do
            let(:valid_rules) {
              [
                { type: 'string', id: 'read', field: 'read', operator: 'not_equal', value: 'false' },
                { type: 'string', id: 'attachments', field: 'attachments', operator: 'is_null', value: nil }
              ]
            }

            it 'returns email read by current user and does not have attachment' do
              emails = described_class.call(valid_auth_data, filter_params.permit!).result

              expect(emails.size).to eq(1)
              expect(emails.first.id).to eq(@email.id)
            end

            context 'when another email in thread has an attachment' do
              before { @another_email.update_column(:external_attachment_count, 1) }

              it 'returns 0 emails' do
                emails = described_class.call(valid_auth_data, filter_params.permit!).result
                expect(emails.size).to eq(0)
              end
            end
          end
        end
      end

      context 'when relative date operators are applied' do
        context 'when last_n_days operator is applied on createdAt' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'last_n_days', value: 3 }
            ]
          }

          before do
            thread1 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @email_1_day = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread1.id, created_at: 1.day.ago)
            thread2 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @email_2_days = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread2.id, created_at: 2.days.ago)
            thread3 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @email_4_days = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread3.id, created_at: 4.days.ago)
            [@email_1_day, @email_2_days, @email_4_days].each do |e|
              e.related_to << build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
            end
          end

          it 'returns emails created in the last 3 days' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            email_ids = emails.map(&:id)
            expect(email_ids).to include(@email_1_day.id, @email_2_days.id)
            expect(email_ids).not_to include(@email_4_days.id)
          end
        end

        context 'when next_n_days operator is applied on openedAt' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'next_n_days', value: 3 }
            ]
          }

          before do
            thread_1 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            thread_2 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @open_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread_1.id)
            @unopen_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread_2.id)
            [@open_email, @unopen_email].each do |e| e.related_to << build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id) end
            create(:email_track_log, email: @open_email, tenant_id: user.tenant_id, created_at: 1.day.from_now)
          end

          it 'returns emails opened in the next 3 days' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
            expect(emails.first.id).to eq(@open_email.id)
          end
        end

        context 'when next_n_days operator is applied on clickedAt' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'clickedAt', field: 'clickedAt', operator: 'next_n_days', value: 3 }
            ]
          }

          before do
            thread_1 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            thread_2 = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @clicked_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread_1.id)
            @unclicked_email = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread_2.id)
            [@clicked_email, @unclicked_email].each do |e| e.related_to << build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id) end
            create(:email_link_log, email: @clicked_email, tenant_id: user.tenant_id, created_at: 1.day.from_now)
          end

          it 'returns emails clicked in the next 3 days' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            expect(emails.size).to eq(1)
            expect(emails.first.id).to eq(@clicked_email.id)
          end
        end

        context 'when invalid n (zero) is provided for last_n_days' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'last_n_days', value: 0 }
            ]
          }

          it 'raises InvalidDataError' do
            expect { described_class.call(valid_auth_data, filter_params.permit!) }.to raise_error(ExceptionHandler::InvalidDataError)
          end
        end

        context 'when invalid n (negative) is provided for next_n_days' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'next_n_days', value: -1 }
            ]
          }

          it 'raises InvalidDataError' do
            expect { described_class.call(valid_auth_data, filter_params.permit!) }.to raise_error(ExceptionHandler::InvalidDataError)
          end
        end

        context 'when invalid n (greater than 364) is provided for next_n_days' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'openedAt', field: 'openedAt', operator: 'next_n_days', value: 366 }
            ]
          }

          it 'raises InvalidDataError' do
            expect { described_class.call(valid_auth_data, filter_params.permit!) }.to raise_error(ExceptionHandler::InvalidDataError)
          end
        end

        context 'when timezone is specified for last_n_days' do
          let(:valid_rules) {
            [
              { type: 'date', id: 'createdAt', field: 'createdAt', operator: 'last_n_days', value: 3, timeZone: 'UTC' }
            ]
          }

          before do
            thread = create(:email_thread, owner: user, tenant_id: user.tenant_id)
            @email_in_range = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, created_at: 2.days.ago)
            @email_out_range = create(:email, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id, created_at: 4.days.ago)
            [@email_in_range, @email_out_range].each do |e|
              e.related_to << build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
            end
          end

          it 'applies timezone correctly' do
            emails = described_class.call(valid_auth_data, filter_params.permit!).result

            email_ids = emails.map(&:id)
            expect(email_ids).to include(@email_in_range.id)
            expect(email_ids).not_to include(@email_out_range.id)
          end
        end
      end
    end

    context "pagination params in filter_params" do
      let(:number_of_emails) { 3 }

      before do
        thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        @emails = create_list(:email, number_of_emails, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id)
        @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
        @emails.each { |e| e.related_to << @to_lookup; e.email_thread.update(lead_ids: [@to_lookup.entity_id]) }
        @command = FilterEmailsQuery.call(valid_auth_data, filter_params)
      end

      context "when pagination is applied" do
        let(:size)          { 2 }
        let(:valid_rule)    { { 'type': 'related_lookup', 'field': 'related_to', 'operator': 'equal', 'value': { 'id': @to_lookup.entity_id, 'entity': @to_lookup.entity_type }} }
        let(:filter_params) { ActionController::Parameters.new({ page: 1, size: size, jsonRule: { "condition": "AND", "rules": [valid_rule] }} ).permit! }

        it "returns correct number of records" do
          expect(@command.result.size).to eq(1)
        end

        it "returns correct email threads" do
          emails = Email.where(id: @command.result.collect(&:id)).as_json
          email_thread_ids = emails.map {|m| m['email_thread_id']}
          expect(email_thread_ids).to eq([emails.last['email_thread_id']])
        end
      end

      context "when pagination is not applied" do
        let(:default_size)  { 10 }
        let(:valid_rule)    { { 'type': 'related_lookup', 'field': 'related_to', 'operator': 'equal', 'value': { 'id': @to_lookup.entity_id, 'entity': @to_lookup.entity_type }} }
        let(:filter_params) { ActionController::Parameters.new({ jsonRule: { "condition": "AND", "rules": [valid_rule] }} ).permit! }

        it "returns correct number of records as per default pagination" do
          expect(@command.result.size).to eq(1)
        end

        it "returns correct email threads" do
          emails=  Email.where(id: @command.result.collect(&:id)).as_json
          emails_ids = emails.map{|m| m['email_thread_id']}

          expect(emails_ids).to eq([emails.last['email_thread_id']])
        end
      end
    end

    context 'when sorting is applied' do
      let(:filter_params) { ActionController::Parameters.new({ page: 1, size: 10, jsonRule: nil, sort: sort_params } ).permit! }

      before do
        thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        @emails = create_list(:email, 3, owner: user, tenant_id: user.tenant_id, email_thread_id: thread.id)
        @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id)
        @emails.each { |e| e.related_to << @to_lookup ; e.email_thread.update(lead_ids: [@to_lookup.entity_id]) }
      end

      context 'when sorting is applied on valid field' do
        context 'when order is asc' do
          let(:sort_params) { "sentAt,asc" }

          it 'returns first email of thread' do
            command = described_class.call(valid_auth_data, filter_params)

            expect(command.success?).to be_truthy
            expect(command.result.first.id).to eq(@emails.first.id)
          end
        end

        context 'when order is desc' do
          let(:sort_params) { "createdAt,desc" }

          it 'returns last email of thread' do
            command = described_class.call(valid_auth_data, filter_params)

            expect(command.success?).to be_truthy
            expect(command.result.first.id).to eq(@emails.last.id)
          end
        end
      end

      context 'when sorting is applied on invalid field' do
        let(:sort_params) { "updatedAt,desc" }

        it 'raises & logs error' do
          expect(Rails.logger).to receive(:error).with("Invalid sort params #{sort_params}")
          expect do
            described_class.call(valid_auth_data, filter_params)
          end.to raise_error(ExceptionHandler::InvalidDataError, '01603001')
        end
      end

      context 'when sorting is applied on invalid order' do
        let(:sort_params) { "sentAt,up" }

        it 'raises & logs error' do
          expect(Rails.logger).to receive(:error).with("Invalid sort params #{sort_params}")
          expect do
            described_class.call(valid_auth_data, filter_params)
          end.to raise_error(ExceptionHandler::InvalidDataError, '01603001')
        end
      end
    end
  end
end
