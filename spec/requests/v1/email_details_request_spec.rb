# frozen_string_literal: true

require 'rails_helper'

RSpec.describe V1::EmailsController, type: :request do
  let(:user)             { create(:user, tenant: create(:tenant) )}
  let(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let(:headers)          { valid_headers(valid_auth_token) }
  let(:email_thread) { create(:email_thread, owner: user, tenant_id: user.tenant_id) }
  let(:email) { create(:email, owner: user, tenant_id: user.tenant_id, email_thread: email_thread) }

  describe '#show' do
    context 'email details' do
      before { get "/v1/emails/#{email.id}", headers: headers }

      it 'email details' do
        email_details = response.parsed_body

        sender = {
          "id"=> email.sender.entity_id,
          "entity"=> email.sender.entity_type,
          "name"=> email.sender.name || email.sender.email
        }

        sender["email"] = email.sender.email if [<PERSON><PERSON><PERSON>UP_USER, LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_CUSTOM_EMAIL].include?(email.sender.entity_type)

        expect(email_details).to eq({
          "id"=> email.id,
          "body"=> "Email body goes here",
          "sender"=> sender,
          "relatedTo"=> [],
          "toRecipients"=> [],
          "ccRecipients"=> [],
          "bccRecipients"=> [],
          "subject"=> "MyText",
          "sentAt"=> email.created_at.iso8601(3),
          "status"=> "received",
          "trackingEnabled"=> false,
          "tenantId"=> email.tenant_id,
          "threadId"=> email.email_thread_id,
          "owner"=> {
            "id"=> email.owner.id,
            "name"=> email.owner.name
          },
          "direction"=> "received",
          "linksClickedAt"=> [],
          "attachments"=> [],
          "openedAt"=> [],
          "globalMessageId"=> email.global_message_id,
          "bounceType"=> nil,
          "failedReason"=> nil
        })
      end
    end
  end
end
