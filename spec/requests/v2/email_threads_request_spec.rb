# frozen_string_literal: true

require 'rails_helper'
require 'bunny-mock'

RSpec.describe V2::EmailThreadsController, type: :request do
  let!(:user)             { create(:user)}
  let!(:another_user)     { create(:user, tenant_id: user.tenant_id)}
  let!(:valid_auth_token) { build(:auth_token, user_id: user.id, tenant_id: user.tenant_id, username: user.name ) }
  let!(:headers)          { valid_headers(valid_auth_token) }
  let(:connected_account) { create(:connected_account, expires_at: (Time.now + 1.hour).to_i, user: user) }
  let(:email)             { build(:email, connected_account: connected_account) }
  let(:other_tenant_user)     { create(:user, tenant: create(:tenant, id: (user.tenant_id + 1)))}

  before do
    connection = BunnyMock.new
    channel = connection.start.channel
    exchange = channel.topic EMAIL_EXCHANGE

    queue = channel.queue "email.update.deal.metaInfo"
    queue.bind exchange, routing_key: "email.update.deal.metaInfo"
    allow(RabbitmqConnection).to receive(:get_exchange).with(EMAIL_EXCHANGE).and_return(queue)
  end

  describe '#search' do
    let(:number_of_emails) { 3 }

    before do
      user_look_up = create(:look_up, entity_type: LOOKUP_USER, entity_id: another_user.id)
      thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
      @emails = create_list(:email, number_of_emails, owner: another_user, sender: user_look_up, tenant_id: user.tenant_id, email_thread_id: thread.id) do |email, index|
        email.subject = index == 0 ? 'Original Subject' : 'Re: Original Subject'
        email.created_at = (10 - index).days.ago
        email.read_by << user.id
        email.save!
        email.attachments.create(file_name: 'test.txt', file_size: 100)
        email.attachments.create(file_name: 'test1.txt', file_size: 200)
      end
      email_thread_attachments_count = Attachment.select(:id).where(inline: false).joins(:email).where(emails: { tenant_id: user.tenant_id }).group('attachments.email_id').count
      email_thread_attachments_count.each do |email_id, attachment_count|
        Email.where(id: email_id).update_all(external_attachment_count: attachment_count)
      end
      @to_lookup = build(:look_up, entity_type: LOOKUP_LEAD, tenant_id: user.tenant_id, owner_id: user.id)
      @cc_lookup = build(:look_up, entity_type: LOOKUP_CONTACT, tenant_id: user.tenant_id)
      @emails.each { |e| e.related_to << @to_lookup }
      @emails.last.email_thread.update(lead_ids: [@to_lookup.entity_id], contact_ids: [@cc_lookup.entity_id])

      [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
        stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
          .with(
            headers: {
              Authorization: "Bearer #{valid_auth_token.token}"
            }
          )
          .to_return(status: 200, body: { accessByOwners: { another_user.id => { email: true } }, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
      end
    end

    context 'When request is invalid' do
      before { post '/v2/email-threads/search', params: {}, headers: invalid_headers }

      it 'returns a failure message' do
        expect(response.parsed_body['errorCode']).to match('01601005')
      end
    end

    context 'when JSON Rule is invalid' do
      before { post '/v2/email-threads/search', params: request_params, headers: headers }

      context 'when json rule for direction field is present' do
        context 'when operator is invalid' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'begins_with',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": 'sent'
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          it 'should returns error response' do
            expect(response.parsed_body).to eq({ 'errorCode' => '01603001' })
          end
        end

        context 'when operator is valid but value is invalid' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'equal',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": 'sent-emails'
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          it 'should returns error response' do
            expect(response.parsed_body).to eq({ 'errorCode' => '01603001' })
          end
        end
      end
    end

    context 'when json rule for direction field is empty' do
      let(:request_params) do
        {
          "jsonRule": {
            "condition": 'AND',
            "rules": [
              {
                id: 'multi_field',
                type: 'multi_field',
                field: 'multi_field',
                operator: 'multi_field',
                value: 'Subject'
              }
            ],
            "valid": true
          }
        }.to_json
      end

      before do
        @emails.second_to_last.update(direction: 'received')
        @emails.last.update(direction: 'sent')

      end

      it 'should returns success response - returns all emails with filter values' do
        post '/v2/email-threads/search', params: request_params, headers: headers
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
        expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.last.id)
      end

      it 'should returns success response without recent email section when view is bulk - returns all emails with filter values' do
        post '/v2/email-threads/search?view=bulk', params: request_params, headers: headers
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body['content']).to match_array([{ 'id' => @emails.first.email_thread_id }])
        expect(response.parsed_body['content'].first['emailsCount']).to be_blank
        expect(response.parsed_body['content'].first['recentEmail']).to be_blank
      end
    end

    context 'when json rule for direction field is present' do
      context 'when operator and value are valid' do
        context 'with equal operator' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'equal',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": 'sent'
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          context 'when value is sent' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')

              post '/v2/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.second_to_last.id)
              expect(response.parsed_body['content'].first['recentEmail']['direction']).to eq('sent')
            end
          end

          context 'when value is received' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')

              updated_params = JSON.parse(request_params)
              updated_params['jsonRule']['rules'].first['value'] = 'received'

              post '/v2/email-threads/search', params: updated_params.to_json, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.last.id)
              expect(response.parsed_body['content'].first['recentEmail']['direction']).to eq('received')
            end
          end
        end

        context 'with not_equal operator' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'not_equal',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": 'sent'
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          context 'when value is sent' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')
              setting = Google::Apis::GmailV1::SendAs.new(display_name: nil)
            allow_any_instance_of(Google::Apis::GmailV1::GmailService).to receive(:get_user_setting_send_as).with('me', connected_account.email).and_return(setting)

              post '/v2/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
              expect(response.parsed_body['content'].first['recentEmail']['direction']).not_to eq('sent')
            end
          end

          context 'when value is received' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')

              updated_params = JSON.parse(request_params)
              updated_params['jsonRule']['rules'].first['value'] = 'received'

              post '/v2/email-threads/search', params: updated_params.to_json, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
              expect(response.parsed_body['content'].first['recentEmail']['direction']).not_to eq('received')
            end
          end
        end

        context 'with is_not_null operator' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'is_not_null',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": nil
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          context 'when values are available for all emails' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')

              post '/v1/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_present
            end
          end
        end

        context 'with is_null operator' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'is_null',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": nil
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          context 'when values are empty for some emails' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: nil)

              post '/v1/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_present
            end
          end

          context 'when values are available for all emails' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'sent')

              post '/v1/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_empty
            end
          end
        end

        context 'with in operator' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'in',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": ['sent', 'received']
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          context 'when values are invalid' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')
              invalid_params = JSON.parse(request_params)
              invalid_params['jsonRule']['rules'].first['value'] = ['sent', 'received', 'draft']

              post '/v1/email-threads/search', params: invalid_params.to_json, headers: headers
            end

            it 'should returns error response' do
              expect(response.parsed_body).to eq({ 'errorCode' => '01603001' })
            end
          end

          context 'when values are valid' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')

              post '/v1/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_present
            end
          end
        end

        context 'with not_in operator' do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'not_in',
                    "id": 'direction',
                    "field": 'direction',
                    "type": 'string',
                    "value": ['sent', 'received']
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          context 'when values are invalid' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')
              invalid_params = JSON.parse(request_params)
              invalid_params['jsonRule']['rules'].first['value'] = ['sent', 'received', 'draft']

              post '/v1/email-threads/search', params: invalid_params.to_json, headers: headers
            end

            it 'should returns error response' do
              expect(response.parsed_body).to eq({ 'errorCode' => '01603001' })
            end
          end

          context 'when values are valid' do
            before do
              @emails.first.update(direction: 'received')
              @emails.second_to_last.update(direction: 'sent')
              @emails.last.update(direction: 'received')

              post '/v1/email-threads/search', params: request_params, headers: headers
            end

            it 'should returns success response - returns emails with filtered by direction field' do
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_empty
            end
          end
        end
      end
    end

    context 'when json rule for user field is present' do
      context 'when operator and value are valid' do
        context "when operator is 'equal'" do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'equal',
                    "id": 'user',
                    "field": 'user',
                    "type": 'long',
                    "value": @emails.last.connected_account.user_id
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          it 'should returns success response - returns emails with filtered by user field' do
            post '/v2/email-threads/search', params: request_params, headers: headers

            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
            expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.last.id)
          end
        end

        context "when operator is 'not equal'" do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'not_equal',
                    "id": 'user',
                    "field": 'user',
                    "type": 'long',
                    "value": @emails.last.connected_account.user_id
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          it 'should returns success response - returns emails with filtered by user field' do
            post '/v2/email-threads/search', params: request_params, headers: headers

            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
            expect(response.parsed_body['content'].first['recentEmail']['id']).not_to eq(@emails.last.id)
          end
        end

        context "when operator is 'is null'" do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'is_null',
                    "id": 'user',
                    "field": 'user',
                    "type": 'long',
                    "value": nil
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          it 'should returns success response - returns emails with filtered by user field' do
            post '/v2/email-threads/search', params: request_params, headers: headers

            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_empty
          end
        end

        context "when operator is 'is not null'" do
          let(:request_params) do
            {
              "jsonRule": {
                "condition": 'AND',
                "rules": [
                  {
                    "operator": 'is_not_null',
                    "id": 'user',
                    "field": 'user',
                    "type": 'long',
                    "value": nil
                  }
                ],
                "valid": true
              }
            }.to_json
          end

          it 'should returns success response - returns emails with filtered by user field' do
            post '/v2/email-threads/search', params: request_params, headers: headers

            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
          end
        end

        context "when operator is 'in'" do
          context 'when only one value is selected' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'in',
                      "id": 'user',
                      "field": 'user',
                      "type": 'long',
                      "value": [@emails.first.connected_account.user_id]
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            it 'should returns success response - returns emails with filtered by user field' do
              post '/v2/email-threads/search', params: request_params, headers: headers
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_present
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.first.id)
            end
          end

          context 'when multiple value is selected' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'in',
                      "id": 'user',
                      "field": 'user',
                      "type": 'long',
                      "value": [@emails.first.connected_account.user_id, @emails.second.connected_account.user_id]
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            it 'should returns success response - returns emails with filtered by user field' do
              post '/v2/email-threads/search', params: request_params, headers: headers
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_present
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.second.id)
            end
          end
        end

        context "when operator is 'not in'" do
          context 'when only one value is selected' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'not_in',
                      "id": 'user',
                      "field": 'user',
                      "type": 'long',
                      "value": [@emails.first.connected_account.user_id]
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            it 'should returns success response - returns emails with filtered by user field' do
              post '/v2/email-threads/search', params: request_params, headers: headers
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_present
              expect(response.parsed_body['content'].first['recentEmail']['id']).not_to eq(@emails.first.id)
            end
          end

          context 'when multiple value is selected' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'not_in',
                      "id": 'user',
                      "field": 'user',
                      "type": 'long',
                      "value": [
                        @emails.first.connected_account.user_id,
                        @emails.second.connected_account.user_id,
                        @emails.third.connected_account.user_id
                      ]
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            it 'should returns success response - returns emails with filtered by user field' do
              post '/v2/email-threads/search', params: request_params, headers: headers
              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_empty
            end
          end
        end
      end
    end

    context 'when json rule for relative filters on date fields' do
      context 'when correct relative filters are applied' do
        let(:another_user_look_up) do
          create(:look_up, entity_type: LOOKUP_USER, entity_id: user.id)
        end

        let(:another_thread1) do
          create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        end

        let(:another_thread2) do
          create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        end

        let(:another_thread3) do
          create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
        end

        context 'with today filter' do
          let(:today_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'today', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 1.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 3.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: today_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email2.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email2.id])
          end
        end

        context 'with yesterday filter' do
          let(:yesterday_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'yesterday', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').yesterday.utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').tomorrow.utc,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: yesterday_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email2.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email2.id])
          end
        end

        context 'with tomorrow filter' do
          let(:tomorrow_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'tomorrow', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 2.hours,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').tomorrow.utc,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: tomorrow_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email3.id])
          end
        end

        context 'with last_seven_days filter' do
          let(:last_seven_days_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_seven_days', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 2.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 3.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 30.hours,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_seven_days_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email2.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email2.id])
          end
        end

        context 'with next_seven_days filter' do
          let(:next_seven_days_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_seven_days', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 2.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 3.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 30.hours,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_seven_days_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email2.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email2.id, @email3.id])
          end
        end

        context 'with last_fifteen_days filter' do
          let(:last_fifteen_days_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_fifteen_days', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 2.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 3.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 15.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_fifteen_days_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@emails.first.email_thread.id, @email1.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email1.id, @email3.id])
          end
        end

        context 'with next_fifteen_days filter' do
          let(:next_fifteen_days_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_fifteen_days', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 1.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 3.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 15.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_fifteen_days_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email2.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email2.id, @email3.id])
          end
        end

        context 'with last_thirty_days filter' do
          let(:last_thirty_days_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_thirty_days', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 1.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 3.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 30.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_thirty_days_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@emails.first.email_thread_id, @email2.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email2.id, @email3.id])
          end
        end

        context 'with next_thirty_days filter' do
          let(:next_thirty_days_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_thirty_days', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 1.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 3.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 30.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_thirty_days_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email3.id])
          end
        end

        context 'with current_week filter' do
          let(:current_week_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'current_week', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 10.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: current_week_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email2.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email2.id])
          end
        end

        context 'with last_week filter' do
          let(:last_week_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_week', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: (Time.zone.now.in_time_zone('Etc/GMT+12').last_week.beginning_of_week + 1.days).utc,
              read_by: [user.id]
            )
            @emails.first.update(created_at: 2.years.ago)
            @emails.second.update(created_at: 2.years.ago)
            @emails.last.update(created_at: (Time.zone.now.in_time_zone('Etc/GMT+12').last_week.beginning_of_week + 2.days).utc)
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_week_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@emails.first.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email3.id])
          end
        end

        context 'with next_week filter' do
          let(:next_week_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_week', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_week.beginning_of_week + 1.days,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 4.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_week_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id])
          end
        end

        context 'with current_month filter' do
          let(:current_month_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'current_month', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )
            @emails.first.update(created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc)
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: current_month_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @emails.first.email_thread_id,
                @email1.email_thread_id,
                @email2.email_thread_id,
                @email3.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.first.id, @email1.id, @email2.id, @email3.id])
          end
        end

        context 'with last_month filter' do
          let(:last_month_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_month', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: (Time.zone.now.in_time_zone('Etc/GMT+12').last_month.beginning_of_month + 2.days).utc,
              read_by: [user.id]
            )
            @emails.each { |email| email.update(created_at: 2.years.ago) }
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_month_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email3.id])
          end
        end

        context 'with next_month filter' do
          let(:next_month_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_month', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_month.beginning_of_month.utc + 2.days,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_month.beginning_of_month.utc + 5.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_month_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email2.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email2.id, @email3.id])
          end
        end

        context 'with current_quarter filter' do
          let(:current_quarter_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'current_quarter', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: current_quarter_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @emails.first.email_thread_id,
                @email1.email_thread_id,
                @email2.email_thread_id,
                @email3.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email1.id, @email2.id, @email3.id])
          end
        end

        context 'with last_quarter filter' do
          let(:last_quarter_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_quarter', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc - 3.months,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_quarter_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email3.id])
          end
        end

        context 'with next_quarter filter' do
          let(:next_quarter_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_quarter', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_quarter.beginning_of_quarter.utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_quarter.beginning_of_quarter.utc + 1.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_quarter_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email2.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email2.id, @email3.id])
          end
        end

        context 'with current_year filter' do
          let(:current_year_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'current_year', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').end_of_year.utc + 2.months,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').beginning_of_year.utc + 1.months,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').beginning_of_year.utc + 1.days,
              read_by: [user.id]
            )
            @emails.each do |email|
              email.update(created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_year.utc + 2.months)
            end
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: current_year_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @email2.email_thread_id,
                @email3.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email2.id, @email3.id])
          end
        end

        context 'with last_year filter' do
          let(:last_year_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'last_year', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').last_year.utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').last_year.utc - 10.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: last_year_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email3.id])
          end
        end

        context 'with next_year filter' do
          let(:next_year_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'next_year', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_year.utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').next_year.utc + 10.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: next_year_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email3.id])
          end
        end

        context 'with week_to_date filter' do
          let(:week_to_date_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'week_to_date', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 10.days,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: week_to_date_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email1.email_thread_id, @email2.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email1.id, @email2.id])
          end
        end

        context 'with month_to_date filter' do
          let(:month_to_date_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'month_to_date', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )
            @emails.first.update(created_at: 10.seconds.ago)
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: month_to_date_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @emails.first.email_thread_id,
                @email1.email_thread_id,
                @email2.email_thread_id,
                @email3.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.first.id, @email1.id, @email2.id, @email3.id])
          end
        end

        context 'with quarter_to_date filter' do
          let(:quarter_to_date_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'quarter_to_date', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: quarter_to_date_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @emails.first.email_thread_id,
                @email1.email_thread_id,
                @email2.email_thread_id,
                @email3.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email1.id, @email2.id, @email3.id])
          end
        end

        context 'with year_to_date filter' do
          let(:year_to_date_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'year_to_date', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: year_to_date_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @emails.first.email_thread_id,
                @email1.email_thread_id,
                @email2.email_thread_id,
                @email3.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email1.id, @email2.id, @email3.id])
          end
        end

        context 'with before_current_date_and_time filter' do
          let(:before_current_date_and_time_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'before_current_date_and_time', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 2.hours,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: before_current_date_and_time_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array(
              [
                @emails.first.email_thread_id,
                @email1.email_thread_id,
                @email2.email_thread_id
              ]
            )
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@emails.last.id, @email1.id, @email2.id])
          end
        end

        context 'with after_current_date_and_time filter' do
          let(:after_current_date_and_time_json_rule) do
            {
              'jsonRule' => {
                'condition' => 'AND',
                'rules' => [
                  {
                    'operator' => 'after_current_date_and_time', 'id' => 'createdAt',
                    'field' => 'createdAt', 'type' => 'date',
                    'value' => nil, 'timeZone' => 'Etc/GMT+12'
                  }
                ],
                'valid' => true
              }
            }
          end

          before do
            @email1 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread1,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email2 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread2,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc,
              read_by: [user.id]
            )

            @email3 = create(
              :email,
              owner: user,
              sender: another_user_look_up,
              tenant_id: user.tenant_id,
              email_thread: another_thread3,
              created_at: Time.zone.now.in_time_zone('Etc/GMT+12').utc + 2.hours,
              read_by: [user.id]
            )
          end

          it 'should returns success response - returns emails with specified filters' do
            post '/v2/email-threads/search', params: after_current_date_and_time_json_rule.to_json, headers: headers
            expect(response).to have_http_status(:ok)
            expect(response.parsed_body['content']).to be_present
            expect(
              response.parsed_body['content'].map { |res| res['id'] }
            ).to match_array([@email3.email_thread_id])
            expect(
              response.parsed_body['content'].map { |res| res['recentEmail']['id'] }
            ).to match_array([@email3.id])
          end
        end
      end
    end

    context 'when json rule for entity fields types' do
      context 'when rule is set for user fields' do
        context 'when propery is teams' do
          context 'operator is equal' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'equal',
                      "id": 'userFields',
                      "field": 'userFields',
                      "type": 'long',
                      "value": 123,
                      "property": 'teams',
                      "primaryField": 'user'
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            let(:expected_body) do
              {
                'fields' => [],
                'jsonRule' => {
                  'id' => 'teams',
                  'field' => 'teams',
                  'type' => 'long',
                  'input' => nil,
                  'operator' => 'equal',
                  'value' => 123,
                  'data' => nil,
                  'property' => nil,
                  'primaryField' => nil,
                  'condition' => nil,
                  'not' => nil,
                  'rules' => nil,
                  'group' => false
                }
              }
            end

            it 'should returns all emails which associated with team user ids' do
              stub_request(:post, 'http://localhost:8081/v1/users/search-for-id')
                .with(body: expected_body)
                .to_return(status: 200, body: [@emails.last.connected_account.user_id].to_json, headers: {})

              post '/v2/email-threads/search', params: request_params, headers: headers

              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.last.id)
            end
          end

          context "when operator is not equal" do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'not_equal',
                      "id": 'userFields',
                      "field": 'userFields',
                      "type": 'long',
                      "value": 123,
                      "property": 'teams',
                      "primaryField": 'user'
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            let(:expected_body) do
              {
                'fields' => [],
                'jsonRule' => {
                  'id' => 'teams',
                  'field' => 'teams',
                  'type' => 'long',
                  'input' => nil,
                  'operator' => 'not_equal',
                  'value' => 123,
                  'data' => nil,
                  'property' => nil,
                  'primaryField' => nil,
                  'condition' => nil,
                  'not' => nil,
                  'rules' => nil,
                  'group' => false
                }
              }
            end

            it 'should returns all emails which is not associated with team user ids' do
              stub_request(:post, 'http://localhost:8081/v1/users/search-for-id')
                .with(body: expected_body)
                .to_return(
                  status: 200,
                  body: [
                    @emails.first.connected_account.user_id,
                    @emails.second.connected_account.user_id
                  ].to_json, headers: {}
                )

              post '/v2/email-threads/search', params: request_params, headers: headers

              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(2)
              expect(response.parsed_body['content'].first['recentEmail']['id']).not_to eq(@emails.last.id)
            end
          end
        end
      end

      context 'when rule is set for sentBy fields' do
        context 'when propery is teams' do
          let(:new_user) { create(:user) }
          let(:new_user_valid_auth_token) { build(:auth_token, user_id: new_user.id, tenant_id: new_user.tenant_id, username: new_user.name ) }
          let(:new_user_request_headers) { valid_headers(new_user_valid_auth_token) }
          let(:new_email) do
            create(
              :email,
              owner: new_user,
              sender: create(:look_up, entity_type: LOOKUP_USER, entity_id: new_user.id),
              tenant_id: new_user.tenant_id,
              email_thread_id: create(:email_thread, owner: new_user, tenant_id: new_user.tenant_id).id,
              subject: 'New Email Subject',
              created_at: 2.years.ago,
              read_by: new_user
            )
          end

          before(:each) do
            [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
              stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
                .with(
                  headers: {
                    Authorization: "Bearer #{new_user_valid_auth_token.token}"
                  }
                )
                .to_return(status: 200, body: { accessByOwners: { new_user.id => { email: true } }, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
            end
          end

          context 'operator is equal' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'equal',
                      "id": 'sentByFields',
                      "field": 'sentByFields',
                      "type": 'long',
                      "value": 123,
                      "property": 'teams',
                      "primaryField": 'sentBy'
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            let(:expected_body) do
              {
                'fields' => [],
                'jsonRule' => {
                  'id' => 'teams',
                  'field' => 'teams',
                  'type' => 'long',
                  'input' => nil,
                  'operator' => 'equal',
                  'value' => 123,
                  'data' => nil,
                  'property' => nil,
                  'primaryField' => nil,
                  'condition' => nil,
                  'not' => nil,
                  'rules' => nil,
                  'group' => false
                }
              }
            end

            it 'should returns all emails which associated with teams' do
              stub_request(:post, 'http://localhost:8081/v1/users/search-for-id')
                .with(body: expected_body)
                .to_return(status: 200, body: [new_email.sender.entity_id].to_json, headers: {})

              post '/v2/email-threads/search', params: request_params, headers: new_user_request_headers

              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(0)
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(new_email.id)
            end
          end

          context "when operator is not equal" do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'not_equal',
                      "id": 'sentByFields',
                      "field": 'sentByFields',
                      "type": 'long',
                      "value": 123,
                      "property": 'teams',
                      "primaryField": 'sentBy'
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            let(:expected_body) do
              {
                'fields' => [],
                'jsonRule' => {
                  'id' => 'teams',
                  'field' => 'teams',
                  'type' => 'long',
                  'input' => nil,
                  'operator' => 'not_equal',
                  'value' => 123,
                  'data' => nil,
                  'property' => nil,
                  'primaryField' => nil,
                  'condition' => nil,
                  'not' => nil,
                  'rules' => nil,
                  'group' => false
                }
              }
            end

            it 'should returns all emails which is not associated with teams' do
              stub_request(:post, 'http://localhost:8081/v1/users/search-for-id')
                .with(body: expected_body)
                .to_return(
                  status: 200,
                  body: [].to_json, headers: {}
                )

              post '/v2/email-threads/search', params: request_params, headers: new_user_request_headers

              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_empty
            end
          end
        end
      end

      context 'when rule is set for receivedBy fields' do
        context 'when propery is teams' do
          let(:new_user) { create(:user) }
          let(:new_user_valid_auth_token) { build(:auth_token, user_id: new_user.id, tenant_id: new_user.tenant_id, username: new_user.name ) }
          let(:new_user_request_headers) { valid_headers(new_user_valid_auth_token) }

          let(:cc_lookup) do
            build(
              :look_up,
              entity_type: LOOKUP_USER,
              tenant_id: new_user.tenant_id,
              owner_id: new_user.id
            )
          end

          let(:email_look_up) do
            create(
              :email_look_up,
              look_up: cc_lookup,
              recipient_type: :cc,
              tenant_id: new_user.tenant_id,
              email_id: new_email.id,
              related: true
            )
          end

          let(:new_email) do
            create(
              :email,
              owner: new_user,
              sender: create(:look_up, entity_type: LOOKUP_USER, entity_id: new_user.id),
              tenant_id: new_user.tenant_id,
              email_thread_id: create(:email_thread, owner: new_user, tenant_id: new_user.tenant_id).id,
              subject: 'New Email Subject',
              created_at: 2.years.ago,
              read_by: new_user
            )
          end

          before(:each) do
            [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
              stub_request(:get, "#{SERVICE_CONFIG}/v1/internal/share/access/#{entity.upcase}/EMAIL")
                .with(
                  headers: {
                    Authorization: "Bearer #{new_user_valid_auth_token.token}"
                  }
                )
                .to_return(status: 200, body: { accessByOwners: { new_user.id => { email: true } }, accessByRecords: { "123" => { read: true, email: true } } }.to_json)
            end
          end

          context 'operator is equal' do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'equal',
                      "id": 'receivedByFields',
                      "field": 'receivedByFields',
                      "type": 'long',
                      "value": 123,
                      "property": 'teams',
                      "primaryField": 'receivedBy'
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            let(:expected_body) do
              {
                'fields' => [],
                'jsonRule' => {
                  'id' => 'teams',
                  'field' => 'teams',
                  'type' => 'long',
                  'input' => nil,
                  'operator' => 'equal',
                  'value' => 123,
                  'data' => nil,
                  'property' => nil,
                  'primaryField' => nil,
                  'condition' => nil,
                  'not' => nil,
                  'rules' => nil,
                  'group' => false
                }
              }
            end

            it 'should returns all emails which associated with teams' do
              stub_request(:post, 'http://localhost:8081/v1/users/search-for-id')
                .with(body: expected_body)
                .to_return(status: 200, body: [email_look_up.look_up.entity_id].to_json, headers: {})

              post '/v2/email-threads/search', params: request_params, headers: new_user_request_headers

              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content'].first['emailsCount']).to eq(0)
              expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(new_email.id)
            end
          end

          context "when operator is not equal" do
            let(:request_params) do
              {
                "jsonRule": {
                  "condition": 'AND',
                  "rules": [
                    {
                      "operator": 'not_equal',
                      "id": 'receivedByFields',
                      "field": 'receivedByFields',
                      "type": 'long',
                      "value": 123,
                      "property": 'teams',
                      "primaryField": 'receivedBy'
                    }
                  ],
                  "valid": true
                }
              }.to_json
            end

            let(:expected_body) do
              {
                'fields' => [],
                'jsonRule' => {
                  'id' => 'teams',
                  'field' => 'teams',
                  'type' => 'long',
                  'input' => nil,
                  'operator' => 'not_equal',
                  'value' => 123,
                  'data' => nil,
                  'property' => nil,
                  'primaryField' => nil,
                  'condition' => nil,
                  'not' => nil,
                  'rules' => nil,
                  'group' => false
                }
              }
            end

            it 'should returns all emails which is not associated with teams' do
              stub_request(:post, 'http://localhost:8081/v1/users/search-for-id')
                .with(body: expected_body)
                .to_return(
                  status: 200,
                  body: [].to_json, headers: {}
                )

              post '/v2/email-threads/search', params: request_params, headers: new_user_request_headers

              expect(response).to have_http_status(:ok)
              expect(response.parsed_body['content']).to be_empty
            end
          end
        end
      end
    end

    [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity|
      context "for related entity - #{entity}" do
        let(:valid_rule)     { { type: 'related_lookup', field: 'related_to', operator: 'equal', value: { id: @to_lookup.entity_id, entity: @to_lookup.entity_type }} }
        let(:request_params) { { jsonRule: { condition: 'AND', rules: [valid_rule] }}.to_json }

        context 'when user has email permission on the entity' do
          context 'and emails are present' do
            before do
              @to_lookup = build(:look_up, entity_type: entity, tenant_id: user.tenant_id, owner_id: user.id)
              @emails.each { |e| e.related_to = [@to_lookup] ; e.email_thread.update("#{entity}_ids": [@to_lookup.entity_id]) }
              @emails.last.related_to = [@cc_lookup]

              thread = create(:email_thread, owner: another_user, tenant_id: user.tenant_id)
              @emails.last.email_thread_id = thread.id
              @emails.last.save!
            end

            context 'when multiple emails with thread - ' do
              before { post '/v2/email-threads/search', params: request_params, headers: headers }

              it 'returns email threads in the response' do
                email_threads = response.parsed_body['content']
                expect(email_threads.count).to eq(1)
                expect(email_threads.map{|e| e['id']}).to match_array(@emails[1].email_thread_id)
              end

              it 'returns expected fields for email in the response' do
                email_threads = response.parsed_body['content']

                expect(email_threads.first.keys).to match_array(%w[id sourceThreadId emailsCount owner recentEmail recordActions])
                expect(email_threads.first['recentEmail'].keys).to match_array(%w[id to associatedTo read subject bodySummary from sentAt attachmentCount ownerId status trackingEnabled openedAt recentLinkClickedAt direction recordActions failedReason])
                expect(email_threads.first['recentEmail']['subject']).to eq('Original Subject')
              end

              it 'returns email source thread id in the response' do
                email_threads = response.parsed_body
                expect(email_threads['content'].map { |email_thread| email_thread['sourceThreadId'] }).to eq(['1..2'])
              end

              it "doesn't return other emails in response" do
                email_threads = response.parsed_body['content']
                expect(email_threads.map{|e| e['id']}).not_to match_array([@emails.last.email_thread_id])
              end

              it "returns correct 'count' in the response for the thread" do
                email_threads = response.parsed_body['content']
                expect(email_threads.first['emailsCount']).to eq(1)
                expect(email_threads.first['recentEmail']['read']).to be_truthy
              end

              it "returns correct 'attachment count' in the response for the thread" do
                email_threads = response.parsed_body['content']
                expect(email_threads.first['recentEmail']['attachmentCount']).to eq(4)
              end

              it 'returns associated entities in response', focus: true do
                ActiveRecord::Base.logger = Logger.new(STDOUT)

                email_threads = response.parsed_body['content']

                expect(email_threads.first['recentEmail']['associatedTo'].count).to eq(1)
              end
            end

            context 'when record actions on email threads' do
              context 'when user is thread owner' do
                before do
                  @emails[1].email_thread.update(owner_id: user.id)
                  post '/v2/email-threads/search', params: request_params, headers: headers
                end

                it 'returns email thread record actions' do
                  email_threads = response.parsed_body['content']

                  expect(email_threads.first['recordActions']['read']).to be_truthy
                  expect(email_threads.first['recordActions']['delete']).to be_truthy
                end
              end

              context 'when user is not thread owner' do
                before { post '/v2/email-threads/search', params: request_params, headers: headers }

                it 'returns email thread record actions' do
                  email_threads = response.parsed_body['content']

                  expect(email_threads.first['recordActions']['read']).to be_truthy
                  expect(email_threads.first['recordActions']['delete']).to be_falsey
                end
              end
            end

            context 'when any one email in thread is not read by current user ' do
              before do
                @emails.first.update(read_by: [])
                post '/v2/email-threads/search', params: request_params, headers: headers
              end
              it 'should return read false' do
                email_threads = response.parsed_body['content']
                expect(email_threads.first['recentEmail']['read']).to be_falsey
              end
            end

            context 'when single email with thread - ' do
              before do
                create_list(:email_thread, 3, owner: another_user, tenant_id: user.tenant_id) do |thread, index|
                  @emails[index].email_thread_id = thread.id
                  thread.update("#{@to_lookup.entity_type}_ids": [@to_lookup.entity_id])
                  @emails[index].save
                end

                post '/v2/email-threads/search', params: request_params, headers: headers
              end

              it "returns correct 'count' in the response for the thread" do
                email_threads = response.parsed_body['content']
                expect(email_threads.first['emailsCount']).to eq(0)
              end

              it "returns correct 'attachment count' in the response for the thread" do
                email_threads = response.parsed_body['content']
                expect(email_threads.first['recentEmail']['attachmentCount']).to eq(2)
              end
            end
          end

          context 'and emails are not present' do
            before do
              @emails.each { |e| e.related_to = [@cc_lookup] }
              post '/v2/email-threads/search', params: request_params, headers: headers
            end

            it 'returns empty in the response' do
              email_threads = response.parsed_body['content']
              expect(email_threads.count).to eq(0)
              expect(email_threads).to eq([])
            end
          end
        end

        context "when user doesn't have email read permission" do
          context 'and emails are present' do
            before do
              expect_any_instance_of(Auth::Data).to receive(:can_read_emails?).and_return(false)
              post '/v2/email-threads/search', params: request_params, headers: headers
            end

            it 'returns error code in the response' do
              expect(response.status).to be(401)
              expect(response.parsed_body['errorCode']).to match('01601005')
            end
          end
        end
      end
    end

    context 'search email or subject' do
      let(:valid_rule) {
        {
          id: 'multi_field',
          type: 'multi_field',
          field: 'multi_field',
          operator: 'multi_field',
          value: 'random'
        }
      }
      let(:request_params) { { jsonRule: { condition: 'AND', rules: [valid_rule] }}.to_json }

      context 'when no email matches' do
        before do
          post '/v2/email-threads/search', params: request_params, headers: headers
        end

        it 'returns blank response' do
          content = response.parsed_body['content']

          expect(response.status).to eq(200)
          expect(content).to eq([])
        end
      end

      context 'when search string matches a substring in to emails' do
        before do
          @emails.first.update(to: ['<EMAIL>', '<EMAIL>'])
          post '/v2/email-threads/search', params: request_params, headers: headers
        end

        it 'returns emails' do
          expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.first.id)
        end
      end

      context 'when search string matches a substring in cc emails' do
        before do
          @emails.first.update(cc: ['<EMAIL>', '<EMAIL>'])
          post '/v2/email-threads/search', params: request_params, headers: headers
        end

        it 'returns emails' do
          expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.first.id)
        end
      end

      context 'when search string matches a substring in bcc emails' do
        before do
          @emails.first.update(bcc: ['<EMAIL>', '<EMAIL>'])
          post '/v2/email-threads/search', params: request_params, headers: headers
        end

        it 'returns emails' do
          expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.first.id)
        end
      end

      context 'when search string matches a substring in from email' do
        before do
          @emails.first.update(from: '<EMAIL>')
          post '/v2/email-threads/search', params: request_params, headers: headers
        end

        it 'returns emails' do
          expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.first.id)
        end
      end

      context 'when search string matches a substring in subject' do
        before do
          @emails.first.update(subject: 'This is randomness subject')
          post '/v2/email-threads/search', params: request_params, headers: headers
        end

        it 'returns emails' do
          expect(response.parsed_body['content'].first['recentEmail']['id']).to eq(@emails.first.id)
        end
      end
    end

    context 'when pagination is applied' do
      let(:valid_rule)     { { type: 'related_lookup', field: 'related_to', operator: 'equal', value: { id: @to_lookup.entity_id, entity: @to_lookup.entity_type }} }
      let(:request_params) { { jsonRule: { condition: 'AND', rules: [valid_rule] }}.to_json }
      let(:page_params)    { "page=#{page_number}&size=#{page_size}" }

      before do
        create_list(:email_thread, 3, owner: another_user, tenant_id: user.tenant_id) do |thread, index|
          @emails[index].email_thread_id = thread.id
          @emails[index].save
          thread.update(lead_ids: [@to_lookup.entity_id])
        end
        @emails.last.related_to = [@to_lookup]
      end

      context 'when page number is 1' do
        let(:page_number) { 1 }

        before { post '/v2/email-threads/search?' + page_params, params: request_params, headers: headers }

        context "and available email threads doesn't exceed the page size" do
          let(:page_size) { 3 }

          it 'returns the correct number of emails threads' do
            email_threads = response.parsed_body['content']
            expect(email_threads.count).to eq(@emails.count)
            expect(email_threads.map{|e| e['id']}).to match_array(@emails.map(&:email_thread_id))
          end

          it 'returns the correct pagination parameters' do
            res = response.parsed_body
            expect(res['totalPages']).to eq(1)
            expect(res['first']).to be true
            expect(res['last']).to be true
            expect(res['page']['no']).to eq(page_number)
            expect(res['page']['size']).to eq(page_size)
          end
        end

        context 'and available email threads exceeds the page size' do
          let(:page_size) { 2 }

          it 'returns the correct number of email threads' do
            email_threads = response.parsed_body['content']
            expect(email_threads.count).to eq(page_size)
            expect(email_threads.map{|e| e['id']}).to match_array(@emails.last(page_size).map(&:email_thread_id))
          end

          it 'returns the correct pagination parameters' do
            res = response.parsed_body
            expect(res['totalPages']).to eq(2)
            expect(res['first']).to be true
            expect(res['last']).to be false
            expect(res['page']['no']).to eq(page_number)
            expect(res['page']['size']).to eq(page_size)
          end
        end
      end

      context 'when page number is other than 1' do
        let(:page_number) { 2 }
        let(:page_size) { 2 }

        before { post '/v2/email-threads/search?' + page_params, params: request_params, headers: headers }

        context "and available emails threads doesn't exceed the page size" do
          it 'returns the correct number of email threads' do
            email_threads = response.parsed_body['content']
            expect(email_threads.count).to eq(1)
            expect(email_threads.map{|e| e['id']}).to match_array([@emails.first.email_thread_id])
          end

          it 'returns the correct pagination parameters' do
            res = response.parsed_body
            expect(res['totalPages']).to eq(2)
            expect(res['first']).to be false
            expect(res['last']).to be true
            expect(res['page']['no']).to eq(page_number)
            expect(res['page']['size']).to eq(page_size)
          end
        end

        context 'and available email threads exceeds the page size' do
          before do
            @additional_emails = create_list(:email, number_of_emails, owner: another_user, tenant_id: user.tenant_id, email_thread_id: [1..5].sample)

            create_list(:email_thread, 3, owner: another_user, tenant_id: user.tenant_id) do |thread, index|
              @additional_emails[index].email_thread_id = thread.id
              @additional_emails[index].save
            end
            @additional_emails.each { |e| e.related_to << @to_lookup ; e.email_thread.update(lead_ids: [@to_lookup.entity_id]) }

            post '/v2/email-threads/search?' + page_params, params: request_params, headers: headers
          end

          it 'returns the correct number of emails' do
            email_threads = response.parsed_body['content']
            expect(email_threads.map{|e| e['id']}).to match_array([@emails.last.email_thread_id, @additional_emails.first.email_thread_id])
          end

          it 'returns the correct pagination parameters' do
            res = response.parsed_body
            expect(res['totalPages']).to eq(3)
            expect(res['first']).to be false
            expect(res['last']).to be false
            expect(res['page']['no']).to eq(page_number)
            expect(res['page']['size']).to eq(page_size)
          end
        end
      end
    end

    context 'when sorting is applied' do
      let(:request_params) { { jsonRule: nil }.to_json }
      let(:request_url) { post "/v2/email-threads/search?#{sort_params}", params: request_params, headers: headers }

      before do
        create_list(:email_thread, 3, owner: another_user, tenant_id: user.tenant_id) do |thread, index|
          @emails[index].email_thread_id = thread.id
          @emails[index].save
          thread.update(lead_ids: [@to_lookup.entity_id])
        end
        @emails.last.related_to = [@to_lookup]
      end

      context 'when asc' do
        let(:sort_params) { 'sort=sentAt,asc' }

        before { request_url }

        it 'returns first email of threads in ascending order' do
          content = response.parsed_body['content']

          expect(content.map { |email| email['recentEmail']['id'] }).to eq(@emails.map(&:id))
        end
      end

      context 'when desc' do
        let(:sort_params) { 'sort=sentAt,desc' }

        before { request_url }

        it 'returns last email of threads in descending order' do
          content = response.parsed_body['content']
          expect(content.map { |email| email['recentEmail']['id'] }).to eq(@emails.map(&:id).reverse)
        end
      end
    end
  end
end
