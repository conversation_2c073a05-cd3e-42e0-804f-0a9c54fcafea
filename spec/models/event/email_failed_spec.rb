# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::EmailFailed do
  let(:user) { create(:user) }
  let(:user_lookup) do
    create(:look_up, entity_type: LOOKUP_USER, entity_id: user.id, tenant_id: user.tenant_id)
  end
  let(:email) { create(:email, sender: user_lookup, tenant_id: user.tenant_id, status: 'failed', failed_reason: '5.1.1 The email account that you tried to reach does not exist', bounce_type: 'hard') }
  let(:email_serialized) do
    {
      "id" => email.id,
      "body" => nil,
      "sender" => {
        "id" => user_lookup.entity_id,
        "entity" => 'user',
        "name" => user_lookup.name,
        "ownerId" => email.sender.owner_id,
        "email" => email.sender.email
      },
      "relatedTo" => [],  
      "toRecipients" => [],
      "ccRecipients" => [],
      "bccRecipients" => [],
      "subject" => email.subject,
      "sentAt" => email.created_at.iso8601(3),  
      "status" => 'sending',
      "trackingEnabled" => email.tracking_enabled,
      "tenantId" => email.tenant_id,
      "threadId" => email.email_thread_id,
      "owner" => {
        "id" => email.owner.id,
        "name" => email.owner.name
      },
      "direction" => email.direction,
      "globalMessageId" => email.global_message_id,
      "bounceType" => nil,
      "failedReason" => nil
    }
  end
  let(:now) { Time.now }

  before do
    allow(Time).to receive(:now).and_return(now)
  end

  let(:event) { described_class.new(email, email_serialized) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('email.failed')
    end
  end

  describe '#to_json' do
    it 'returns event payload' do
      event_payload = JSON.parse(event.to_json)

      expect(event_payload).to eq(
        {
          "entity" => {
            "id" => email.id,
            "body" => nil,
            "sender" => {
              "id" => user_lookup.entity_id,
              "entity" => 'user',
              "name" => user_lookup.name,
              "ownerId" => email.sender.owner_id,
              "email" => email.sender.email
            },
            "relatedTo" => [],  
            "toRecipients" => [],
            "ccRecipients" => [],
            "bccRecipients" => [],
            "subject" => email.subject,
            "sentAt" => email.created_at.iso8601(3),  
            "status" => email.status,
            "trackingEnabled" => email.tracking_enabled,
            "tenantId" => email.tenant_id,
            "threadId" => email.email_thread_id,
            "owner" => {
              "id" => email.owner.id,
              "name" => email.owner.name
            },
            "direction" => email.direction,
            "globalMessageId" => email.global_message_id,
            "bounceType" => 'hard',
            "failedReason" => '5.1.1 The email account that you tried to reach does not exist',
            "failedAt" => now.utc.iso8601(3)
          },
          "oldEntity" => email_serialized,
          "metadata" => {
            "tenantId" => email.tenant_id,
            "userId" => email.owner.id,
            "entityType" => "EMAIL",
            "entityId" => email.id,
            "entityAction" => "EMAIL_FAILED",
            "executedWorkflows" => []
          }
        }
      )
    end
  end
end
