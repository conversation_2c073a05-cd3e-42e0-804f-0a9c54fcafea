# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Event::EmailCreatedWorkflowV2 do
  let!(:email) { create(:email) }
  let(:email_serialized_data) { EmailDetailsSerializer.call(email, false, nil, true, false, add_owner_id: true).result }
  let(:event) { described_class.new(email_serialized_data) }

  describe '#routing_key' do
    it 'returns event name' do
      expect(event.routing_key).to eq('email.created.workflow.v2')
    end
  end

  describe '#to_json' do
    it 'returns event payload' do
      event_payload = JSON.parse(event.to_json)

      expect(event_payload.keys).to match_array(%w[entity oldEntity metadata])
      expect(event_payload['oldEntity']).to eq(nil)
      expect(event_payload['metadata']).to eq(
        {
          "tenantId" => email.tenant_id,
          "userId" => email.owner.id,
          "entityType" => "EMAIL",
          "entityId" => email.id,
          "entityAction" => "CREATED",
          "workflowId" => nil,
          "executedWorkflows" => nil,
          "executeWorkflow" => true,
          "workflowName" => nil
        }
      )

      expect(event_payload['entity']).to eq(
        {
          "id" => email.id,
          "body" => nil,
          "sender" => email_serialized_data['sender'],
          "relatedTo" => [],
          "toRecipients" => [],
          "ccRecipients" => [],
          "bccRecipients" => [],
          "subject" => "MyText",
          "sentAt" => email.created_at.iso8601(3),
          "status" => "received",
          "trackingEnabled" => false,
          "tenantId" => email.tenant_id,
          "threadId" => email.email_thread_id,
          "owner" => {
            "id" => email.owner.id,
            "name" => email.owner.name
          },
          "direction" => "received",
          "attachments" => [],
          "linksClickedAt" => [],
          "openedAt" => [],
          "globalMessageId" => email.global_message_id,
          "bounceType" => nil,
          "failedReason" => nil
        }
      )
    end

    it 'returns event payload when metadata is present' do
      metadata = {
       tenantId: 47,
       userId: 71,
       entityType: "LEAD",
       workflowId: "WF_4152",
       executedWorkflows: [
         "WF_4120"
        ],
       entityAction: "UPDATED",
       executeWorkflow: true,
       entityId: 489958,
       workflowName: "lead updated workflow"
      }

      event_payload = JSON.parse(described_class.new(email_serialized_data, metadata).to_json)

      expect(event_payload.keys).to match_array(%w[entity oldEntity metadata])
      expect(event_payload['metadata']).to eq(
        {
          "tenantId" => email.tenant_id,
          "userId" => email.owner.id,
          "entityType" => "EMAIL",
          "entityId" => email.id,
          "entityAction" => "CREATED",
          "workflowId" => "WF_4152",
          "executedWorkflows" => ["WF_4120"],
          "executeWorkflow" => true,
          "workflowName" => "lead updated workflow"
        }
      )
    end
  end
end
