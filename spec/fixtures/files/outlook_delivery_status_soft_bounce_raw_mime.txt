Received: from MA0P287MB1322.INDP287.PROD.OUTLOOK.COM (2603:1096:a01:f4::6) by
 PN2P287MB1310.INDP287.PROD.OUTLOOK.COM with HTTPS; <PERSON><PERSON>, 26 Aug 2025 06:14:59
 +0000
From: Microsoft Outlook
	<<EMAIL>>
To: <<EMAIL>>
Date: Tue, 26 Aug 2025 06:14:58 +0000
Content-Type: multipart/report; report-type=delivery-status;
	boundary="1a585fe9-3568-4b04-8bdb-74ca6d1d8f95"
X-MS-Exchange-Organization-SCL: 1
X-MS-Exchange-Message-Is-Ndr:
Content-Language: en-GB
Message-ID: <<EMAIL>>
In-Reply-To: <<EMAIL>>
References: <<EMAIL>>
Thread-Topic: attack on titan
Thread-Index: AQHcFlC8rQ9iuP565kavRDjJHiAGTbR0dN2F
Subject: Undeliverable: attack on titan
Auto-Submitted: auto-replied
X-MS-PublicTrafficType: Email
X-MS-Exchange-Organization-AuthSource: MA0P287MB1322.INDP287.PROD.OUTLOOK.COM
X-MS-Exchange-Organization-AuthAs: Internal
X-MS-Exchange-Organization-AuthMechanism: 05
X-MS-Exchange-Organization-Network-Message-Id: e8b52326-bcb2-415e-0823-08dde467e21d
X-MS-TrafficTypeDiagnostic: MA0P287MB1322:EE_|PN2P287MB1310:EE_
X-MS-Exchange-Organization-ExpirationStartTime: 26 Aug 2025 06:14:58.2754
 (UTC)
X-MS-Exchange-Organization-ExpirationStartTimeReason: SideEffectMessage
X-MS-Exchange-Organization-ExpirationInterval: 1:00:00:00.0000000
X-MS-Exchange-Organization-ExpirationIntervalReason: SideEffectMessage
X-Microsoft-Antispam: BCL:0;ARA:13230040|1930700014|69100299015|366016|**********|13003099007|41050700001;
X-Forefront-Antispam-Report: CIP:***************;CTRY:;LANG:en;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:;PTR:;CAT:NONE;SFS:(13230040)(1930700014)(69100299015)(366016)(**********)(13003099007)(41050700001);DIR:INT;
X-MS-Exchange-CrossTenant-OriginalArrivalTime: 26 Aug 2025 06:14:58.2775
 (UTC)
X-MS-Exchange-CrossTenant-FromEntityHeader: Hosted
X-MS-Exchange-CrossTenant-AuthSource: MA0P287MB1322.INDP287.PROD.OUTLOOK.COM
X-MS-Exchange-CrossTenant-AuthAs: Internal
X-MS-Exchange-CrossTenant-Network-Message-Id: e8b52326-bcb2-415e-0823-08dde467e21d
X-MS-Exchange-Transport-CrossTenantHeadersStamped: MA0P287MB1322
X-MS-Exchange-Organization-MessageDirectionality: Originating
X-MS-Exchange-Transport-EndToEndLatency: 00:00:01.1844869
X-MS-Exchange-Processed-By-BccFoldering: 15.20.9052.000
X-Microsoft-Antispam-Mailbox-Delivery:
	ucf:0;jmr:0;auth:0;dest:I;ENG:(910005)(944506478)(944626604)(4710137)(4711095)(920097)(425001)(930097)(140003);
X-Microsoft-Antispam-Message-Info:
	oa27ZsPkFe/75jzlIx8ALs1nJ+oCWcGuUFjGPk4M4mNC9mjmgS6mdUE0JxN0EOS67Gor6QtgzwLcseVWOC1RxvPWkIYlmUtoR523sYc6ER5M2w2r7SNBbyQhcIyXdFfZNJgVCWdtzLDuSoLaWYcmsYsB19FtMXjI6XNwuZdTPBlQKHkYza2eDm9jmIzNN4xbz6WMCuWzac8I2zKeC8js5AXHmsH+F9/oPyR48YA4+xmy9mtuU3jZ9X85gHGXf6iRuStqCxb1RnGLArcN9MFbR/Tm50FlGDwTGc3JNA/W5Axa/f+cW+6QKB7PEaagvAV8BEJCyjt795NpyxXtgniQZr7BSPdKLJhpgXg4ucR2RJXZAzX3ROWchiGt8BD2omAl2Oe74oo2N84GpaP4CdRnbBh1i6qDSdZDJRS0tU4smb5x/6ExEUxQOIgYR0QJPPf34r4JS9KQGAXwI27d+QDRPlTSTLjrykzyMI6DYeRVaZhsBpUAz/HJk9vjJ4gJ5tE+p3zdylcQD4cOYkL4+P1iEZnYce5kNz76TgccjvhO8/KKXvTVlr4ajDtAyiU9RIdsHvNag+V1rQ9ueVZURyhI1ePi4Z/zNbTXyJDKb+qfbSCWOeDPjoH77M9mVjY5MARzyEMsDtVtWtICUkyQYyBdSKpr5QUYZXW0aw1H+iNOp5WvPVKDKBGzYvE10k8z/oUYZdohzZYSa++jRyW9X8gsMerl9pFU4VrWCfUlGNeFKCeFoTwMhRzyIRRCeB/f4ad0t0FbMoe5thMqwE5jbybXBXvt9id6Ce08FFJDCHshI+q0/vILSHFt5TW5XmdTbX7sFS9ycZRUaYU8w2vlLegBS0riTUkYGGYMhlFVtibjWS6fABmOKI3ZbOcSTslHJqgHoJQFvDDnux5iilAErRxabl6BK4l03d14eQtNbFmpQ7tLYaWtV0XtyC2hcFsUXM24
MIME-Version: 1.0

--1a585fe9-3568-4b04-8bdb-74ca6d1d8f95
Content-Type: multipart/alternative; differences=Content-Type;
	boundary="b5c604e1-ba92-4836-b248-2a13725dc494"

--b5c604e1-ba92-4836-b248-2a13725dc494
Content-Type: text/plain; charset="us-ascii"
Content-Transfer-Encoding: quoted-printable

[https://products.office.com/en-us/CMSImages/Office365Logo_Orange.png?versi=
on=3Db8d100a9-0a8b-8e6a-88e1-ef488fee0470]
Your <NAME_EMAIL> couldn't be delivered.
sjkfjklsdjflkjlkjfdsljj wasn't found at gmail.com.
kalpesh.vasave  Office 365      sjkfjklsdjflkjlkjfds. . .
Action Required                 Recipient
Unknown To address

How to Fix It
The address may be misspelled or may not exist. Try one or more of the foll=
owing:

  *   Send the message again following these steps: In Outlook, open this n=
on-delivery report (NDR) and choose Send Again from the Report ribbon. In O=
utlook on the web, select this NDR, then select the link "To send this mess=
age again, click here." Then delete and retype the entire recipient address=
. If prompted with an Auto-Complete List suggestion don't select it. After =
typing the complete address, click Send.
  *   Contact the recipient (by phone, for example) to check that the addre=
ss exists and is correct.
  *   The recipient may have set up email forwarding to an incorrect addres=
s. Ask them to check that any forwarding they've set up is working correctl=
y.
  *   Clear the recipient Auto-Complete List in Outlook or Outlook on the w=
eb by following the steps in this article: Fix email delivery issues for er=
ror code 5.1.1 in Office 365<https://go.microsoft.com/fwlink/?LinkId=3D3893=
63>, and then send the message again. Retype the entire recipient address b=
efore selecting Send.

If the problem continues, forward this message to your email admin. If you'=
re an email admin, refer to the More Info for Email Admins section below.

Was this helpful? Send feedback to Microsoft<https://go.microsoft.com/fwlin=
k/?LinkId=3D525920>.
________________________________

More Info for Email Admins
Status code: 550 5.1.1

This error occurs because the sender sent a message to an email address out=
side of Office 365, but the address is incorrect or doesn't exist at the de=
stination domain. The error is reported by the recipient domain's email ser=
ver, but most often it must be fixed by the person who sent the message. If=
 the steps in the How to Fix It section above don't fix the problem, and yo=
u're the email admin for the recipient, try one or more of the following:

The email address exists and is correct - Confirm that the recipient addres=
s exists, is correct, and is accepting messages.

Synchronize your directories - If you have a hybrid environment and are usi=
ng directory synchronization make sure the recipient's email address is syn=
ced correctly in both Office 365 and in your on-premises directory.

Errant forwarding rule - Check for forwarding rules that aren't behaving as=
 expected. Forwarding can be set up by an admin via mail flow rules or mail=
box forwarding address settings, or by the recipient via the Inbox Rules fe=
ature.

Mail flow settings and MX records are not correct - Misconfigured mail flow=
 or MX record settings can cause this error. Check your Office 365 mail flo=
w settings to make sure your domain and any mail flow connectors are set up=
 correctly. Also, work with your domain registrar to make sure the MX recor=
ds for your domain are configured correctly.

For more information and additional tips to fix this issue, see Fix email d=
elivery issues for error code 550 5.1.1 in Office 365<https://go.microsoft.=
com/fwlink/?LinkId=3D389363>.

Original Message Details
Created Date:   26/08/2025 06:14:54
Sender Address: <EMAIL>
Recipient Address:      <EMAIL>
Subject:        attack on titan

Error Details
Error:  550-5.1.1 The email account that you tried to reach does not exist.=
 Please try 550-5.1.1 double-checking the recipient's email address for typ=
os or 550-5.1.1 unnecessary spaces. For more information, go to 550 5.1.1 h=
ttps://support.google.com/mail/?p=3DNoSuchUser 3f1490d57ef6-e96d57c7011si10=
********.535 - gsmtp
Message rejected by:    mx.google.com

Notification Details
Sent by:        MA0P287MB1322.INDP287.PROD.OUTLOOK.COM

Message Hops
HOP     TIME (UTC)      FROM    TO      WITH    RELAY TIME
1       26/08/2025
06:14:54        PN2P287MB1310.INDP287.PROD.OUTLOOK.COM  PN2P287MB1310.INDP2=
87.PROD.OUTLOOK.COM  mapi    *
2       26/08/2025
06:14:54        PN2P287MB1310.INDP287.PROD.OUTLOOK.COM  MA0P287MB1322.INDP2=
87.PROD.OUTLOOK.COM  Microsoft SMTP Server (version=3DTLS1_2, cipher=3DTLS_=
ECDHE_RSA_WITH_AES_256_GCM_SHA384)    *

Original Message Headers

ARC-Seal: i=3D1; a=3Drsa-sha256; s=3Darcselector10001; d=3Dmicrosoft.com; c=
v=3Dnone;
 b=3DgKQOTBy2nX/Hc++VLkri3r3k1T4WdrfuOLdO1OjdQUU2e5Knlqp+xtOLj3KoyVhvLR+vkp=
+bb8vf5jL/JCi9LqijqJfw90YpNWAoZ8GFCm7kaYrYwXsLGrg6M0lpoqRJpBVAwiTvlOewhutmg=
q5ye5aQQV1l80svBNSLUkJBILYWubNBxe030SIU8AHpcfmMfET2HJQK3IJ6wkqo03Noyr2vpFC3=
mmmq57fa6iq8q5K4qf8poqwb6Td9/tKjTToGhZjAnpwKrp3MDTbyjJyBzq9C+MSbC1QfyZxkV/p=
bIBLcI8WX6E4VxPc0Th6/bCT/h9WNTc4v06eIyHE/aEsZaw=3D=3D
ARC-Message-Signature: i=3D1; a=3Drsa-sha256; c=3Drelaxed/relaxed; d=3Dmicr=
osoft.com;
 s=3Darcselector10001;
 h=3DFrom:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-A=
ntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Ex=
change-AntiSpam-MessageData-1;
 bh=3DqfR+mL8uA95rHBppsrvu5fQB/sVigMuxXmjkeyhyWPc=3D;
 b=3DN7W3qDbW6OXmRPkHt/BKHhaU67/7jkNWIOzbQJtBS1zVaMMR6isc7OO0dTzkrWznEg+etd=
HL0vOy9Glfct9DwB66l6uDxQb9dOplbDKbJEmyyXcl9Ix5I6qR4bZpEcOaUOl1ISRw6AUL2p3bY=
11/Kb9tnuMB0rrLl1nizDUO8/D/0+kv5v+yv/OSGlsRhgsOc4MThT250tBzGKVRUa3Hpmu35B3V=
H2B7boEnoWs1t43hpK2WsNEH8EpFr9SOk7nCSNM3TH0Jk5DnDSSHvhUW1ZMWJ93PDhKz0GWEcYQ=
DPcuDoqrScLTVFmHK2H4dGia31Jub2XNkyjmfPPLPl6v2Xw=3D=3D
ARC-Authentication-Results: i=3D1; mx.microsoft.com 1; spf=3Dpass
 smtp.mailfrom=3Dkylas.io; dmarc=3Dpass action=3Dnone header.from=3Dkylas.i=
o;
 dkim=3Dpass header.d=3Dkylas.io; arc=3Dnone
Received: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM (2603:1096:c01:1b3::1=
1)
 by MA0P287MB1322.INDP287.PROD.OUTLOOK.COM (2603:1096:a01:f4::6) with
 Microsoft SMTP Server (version=3DTLS1_2,
 cipher=3DTLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.9073.13; Tue, 26 =
Aug
 2025 06:14:54 +0000
Received: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
 ([fe80::2574:c107:e23a:1a6b]) by PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
 ([fe80::2574:c107:e23a:1a6b%6]) with mapi id 15.20.9052.019; Tue, 26 Aug 2=
025
 06:14:54 +0000
From: kalpesh vasave <<EMAIL>>
To: "<EMAIL>" <<EMAIL>>
Subject: attack on titan
Thread-Topic: attack on titan
Thread-Index: AQHcFlC8rQ9iuP565kavRDjJHiAGTQ=3D=3D
Date: Tue, 26 Aug 2025 06:14:54 +0000
Message-ID: <PN2P287MB13106C8F6EFBAEC248691958EE39A@PN2P287MB1310.INDP287.P=
ROD.OUTLOOK.COM>
Accept-Language: en-GB, en-US
Content-Language: en-US
X-MS-Has-Attach:
X-MS-TNEF-Correlator:
authentication-results: dkim=3Dnone (message not signed)
 header.d=3Dnone;dmarc=3Dnone action=3Dnone header.from=3Dkylas.io;
x-ms-publictraffictype: Email
x-ms-traffictypediagnostic: PN2P287MB1310:EE_|MA0P287MB1322:EE_
x-ms-office365-filtering-correlation-id: 21c44dab-036d-493b-0dd9-08dde467df=
ad
x-ms-exchange-senderadcheck: 1
x-ms-exchange-antispam-relay: 0
x-microsoft-antispam: BCL:0;ARA:13230040|69100299015|376014|**********|3660=
16|38070700018|**********;
x-microsoft-antispam-message-info: =3D?us-ascii?Q?51YpMwBNwL+5qtUxbhXge+r5M=
fqrJciy7YNI32b2U7+BWz+iUSgsrjuY+tMa?=3D
 =3D?us-ascii?Q?0FBNuAmJy8z+eJe5Sc0K+04BuIyuEbfoGWpH3gQCg/kej7XIwCrPMeoo6Wh=
K?=3D
 =3D?us-ascii?Q?GnssFt8X/4KKtmyFYJpn5WidtQ/BLXlKmjhDZ99c3Saxn/GGFdFAk5cGvv5=
Z?=3D
 =3D?us-ascii?Q?duJaz22jQ3qTZUiP6HpiNNwUU8BliNu5ejukvPjj3Ma/D9oxYKxB6tRzcVH=
x?=3D
 =3D?us-ascii?Q?aPy/5yIDSWD+LtrhxYny6LcoqOazbsf4mQtJ/4YxUXu5tC61/CiwfNIMLPk=
u?=3D
 =3D?us-ascii?Q?CxeezzBvHFGUroKUXIxFz8Oz5WsD3wgh8EfIXwokLaTyZFMzH5VJip+nje1=
3?=3D
 =3D?us-ascii?Q?OLC+L+0yOZ/chgzHcFtKw1gqTjTgL+kSk2zvRVyr2hKy2HVpq3xJtMunQMx=
Y?=3D
 =3D?us-ascii?Q?icGaT+WBJZWFjEZO8e/Hn2F54o7ZT0y5DAB/R5q1qizDgj1FaPRnogC/cRU=
P?=3D
 =3D?us-ascii?Q?gsy1Oq1BoalRi57i5RIcw7J+cuC3J98weeUxfOuxh60s+G6m1ja1LVTEfWK=
2?=3D
 =3D?us-ascii?Q?IfRYfY/YrHqIR4LL5RaRNQTbzWj0IB/0zi2i2XlS5pPkXp7qxb6pzwKzahf=
v?=3D
 =3D?us-ascii?Q?gH3nAaTDAh2VRNotwPIMcl0mmZvVJWVQbxBihqjHbGVUEtwwEGsLeASbdYw=
H?=3D
 =3D?us-ascii?Q?3Ugdnwo47bFETm2DYwLkYfoqVk/1taqEDH9//BiWPtbMDFOQQPjrZpk4zeF=
c?=3D
 =3D?us-ascii?Q?ONY+1E7g7K3QHr8Yqz0AhZlDT3zRTsVT4gC5KwoP0FJgRpu8wcm7hh1X037=
K?=3D
 =3D?us-ascii?Q?7XWpnWfXE66jMXicrFvVQuGwra4gh9mfBPPQ27Sti6l39oZuGThfQlKIVLp=
K?=3D
 =3D?us-ascii?Q?UtWMkgGIJaY1GH23R0qsTuybt6O9Yq1z7wfReTg1LTrR0Ag9Yqlx8uKpw08=
y?=3D
 =3D?us-ascii?Q?72x83PNG/EmQtVdZy6zWh68+sOxn7xi8nYvhk6ZTnsRUBQFwAFT3kKdBZWV=
a?=3D
 =3D?us-ascii?Q?uzzVeCg1thsbFddp1NU9y8hIqKR7pwgx6EiViUn1KdgBr3oIFd8c4/hpL8y=
D?=3D
 =3D?us-ascii?Q?GEUQyPvtQSa8tKUzZFI6wIhwdzvFHmVL/QgYC15JxD+qY0PtZ5tr23ajwsF=
u?=3D
 =3D?us-ascii?Q?dEQ1HPd3hT4u8JSkXEHScpXTfeOkWgr7WcHpwsRZf6ifl1PgOEQOdcNlcVb=
E?=3D
 =3D?us-ascii?Q?QwkqaXQ02Pl/KluFj7c/88H7W9AELqYhzspApqRMMOSTjzmsPwKnHdp1sK5=
O?=3D
 =3D?us-ascii?Q?Ll/opGCbg8CNqT7ymWXL+GfK1Da6FwQygKeR50D6LA+vEvT3i4Npiyl6s4+=
1?=3D
 =3D?us-ascii?Q?gqawoC/y9aIZQpLdv6FGg2Gy5aoot43EwFC2XcCOWgDfHHOaNmp4Yj+11lg=
2?=3D
 =3D?us-ascii?Q?JCp5kMeJmwY5llh0SSsj+I4K+Pln9se3tEBHRLnZ5Mgq9Y3NDrJFJYEnzVU=
C?=3D
 =3D?us-ascii?Q?sYyuA2FAwQoyjwbQWUCfGqu/45DWtjDucMBPbtGIwtUx03XLKt3wBAIBiQO=
o?=3D
 =3D?us-ascii?Q?q8TxaPPULFc=3D3D?=3D
x-forefront-antispam-report: CIP:***************;CTRY:;LANG:en;SCL:1;SRV:;I=
PV:NLI;SFV:NSPM;H:PN2P287MB1310.INDP287.PROD.OUTLOOK.COM;PTR:;CAT:NONE;SFS:=
(13230040)(69100299015)(376014)(**********)(366016)(38070700018)(**********=
);DIR:OUT;SFP:1102;
x-ms-exchange-antispam-messagedata-chunkcount: 1
x-ms-exchange-antispam-messagedata-0: =3D?us-ascii?Q?3Nac/ywb9flnIun6pnhAQw=
M7SKHV5VM9niOdw8+lIqmUzoIb+nTaiJKZwTJB?=3D
 =3D?us-ascii?Q?dTfgTegnMtdsEv/GHXh+uM7cUzEAe01YgoOuOF94DoFWD1U++eGEfn/1t76=
D?=3D
 =3D?us-ascii?Q?veeizzErzW0dbZYLmN7MkyXEcpRPROzqIwEudRpHw5KqoQY7GJhTvPqHb06=
3?=3D
 =3D?us-ascii?Q?R6ouZkSeiWNBrr4yIVSVsT6ROuRKkhBIcw6vEhH+WYJkVdU1X0Qf3k8Nrp6=
/?=3D
 =3D?us-ascii?Q?TcmBFkjEnOVlLUuXFaHo7GAHp02Gfdw80BPKbj815A6ntu/Wcz6zBKZanjp=
T?=3D
 =3D?us-ascii?Q?9RetJRPrVFo/pguXfrGM4XpPWnPBuFw5QJ/WhYddKJ6GezPivyyr5d8OHsN=
n?=3D
 =3D?us-ascii?Q?4xZpH74IhUOZW/LjhwZ1OR6FpwHnmXgz/asrxjdT5a4C3QJMvHc8SZkI3x8=
2?=3D
 =3D?us-ascii?Q?Q/hEji/puaX60+TcrmFKkxPqfCPc4aO4+IUi/vyb7wdgEh8ixnQVR1Hs1Xd=
l?=3D
 =3D?us-ascii?Q?tPFDIrKk4kBkrDaGYHmcO9LuXkNRSoQX9OSpHGazlP+4waRY5IktYt3Md3A=
V?=3D
 =3D?us-ascii?Q?UzohMer6oBBNk2XpAb72/kmOgJdvqKCTQSZs2nRuzKVaavEYizNwMeL8OK5=
T?=3D
 =3D?us-ascii?Q?LBFRG9RwSnXcytu37bBuCaOjPdbRtNlF80bi90snOe2EXZzzMCOe2dfi5Uy=
i?=3D
 =3D?us-ascii?Q?TAnOiIdvNVXxlcrMedHJbj7h+20ETbKZpiQsffz/gJafMppjcXt4AJ8n0NJ=
P?=3D
 =3D?us-ascii?Q?R/7NBA9WfVJcHpaFFJUciNPSpDLyTkWAcQ0gQgmMdvHrLYlFQ8gwx3sNIBT=
p?=3D
 =3D?us-ascii?Q?6JFVM0uRvjV+mamS9bA48GuotfPuOlzzOSgixPkmgZZKzNd6zD/LyxkLrJO=
0?=3D
 =3D?us-ascii?Q?914ZaAzvk57CZW8w4GO1WPgrVkpUcTSRu7PuctU/apPlks+OX4oI3rHllJP=
w?=3D
 =3D?us-ascii?Q?IPbYVdEvoBs1RGWAYpCAdxno25k1T+pwsMGP0aOMVUny/Q6ffZaKoikc6xY=
U?=3D
 =3D?us-ascii?Q?S2Z1x4h7T2gCmDwHLbxkYjqANEYlkS+f6SqP/NtV/29pnELj5yxmyrUTBKW=
G?=3D
 =3D?us-ascii?Q?uaI79iklKfU5G03gJQ+6TihDyuq5S8c2XHFHEco1GVgxTQza6/xy4qxakBB=
3?=3D
 =3D?us-ascii?Q?po2Uq4WoVtIg1OgSCFod/1bxlDLId7AUmhUj08Dv1zNAGuoYC0gHD/BjxDy=
O?=3D
 =3D?us-ascii?Q?zIFya4JHqkiJM35RBSI5gV0le6I4ettCdhhesGSjedARXwPepyEDLESIUdh=
l?=3D
 =3D?us-ascii?Q?Y7v+woBz0eFkKg2+zpyzcv7jr73ZkeJVZVvnyfobixaAGTm8fmjQpj376XJ=
f?=3D
 =3D?us-ascii?Q?kD5qTZEN0AnmwELXdGEj4lRgqefmCdz/icc6Smfd9hnc8BwWgBJGjJAArqu=
f?=3D
 =3D?us-ascii?Q?pGcuMvDsRTqFWx4WND1J1sIAPrbqS+mJ3TqUQ9XFPciElsp6zS0DzcTouAw=
Q?=3D
 =3D?us-ascii?Q?0pd9y5EJyGfJDRhlDwlcWsqo5lhK5ZkFwqqdQT1gHFx/3XpVrW5vIFIYPS+=
K?=3D
 =3D?us-ascii?Q?pXBKhn015KWAAcIKfmCKBvhUJrdXCykSPE154ouR3288SV1nPA7jJhnG2Dj=
R?=3D
 =3D?us-ascii?Q?bnWLm/h2ghKvNfIjHMY1JdYrow3ofPaPS8xebWGEFIMuKsV8pXFo0+yVLYn=
o?=3D
 =3D?us-ascii?Q?pI+9IhPmBhPQ3iiWaIfb+8JjBNnYnfakKQVh38jd?=3D
Content-Type: multipart/alternative;
        boundary=3D"_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB131=
0INDP_"
MIME-Version: 1.0
X-OriginatorOrg: kylas.io
X-MS-Exchange-CrossTenant-AuthAs: Internal
X-MS-Exchange-CrossTenant-AuthSource: PN2P287MB1310.INDP287.PROD.OUTLOOK.CO=
M
X-MS-Exchange-CrossTenant-Network-Message-Id: 21c44dab-036d-493b-0dd9-08dde=
467dfad
X-MS-Exchange-CrossTenant-originalarrivaltime: 26 Aug 2025 06:14:54.1502
 (UTC)
X-MS-Exchange-CrossTenant-fromentityheader: Hosted
X-MS-Exchange-CrossTenant-id: f0dce677-c472-4176-a936-4eb93f005cf5
X-MS-Exchange-CrossTenant-mailboxtype: HOSTED
X-MS-Exchange-CrossTenant-userprincipalname: lEdq7XZgdA4yEyHcA6eodsA1CUUfXt=
mPBcewMnVATUZXgXkSeQ0jMyTqOpAGTjmrgYecWzAE9zsf1avKlf6SYQ=3D=3D
X-MS-Exchange-Transport-CrossTenantHeadersStamped: MA0P287MB1322


--b5c604e1-ba92-4836-b248-2a13725dc494
Content-Type: text/html; charset="us-ascii"
Content-Transfer-Encoding: quoted-printable

<html>
<head>
<meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dus-ascii"=
></head>
<meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D1"=
>
  <head>
    <title>
          DSN
        </title>
  </head>
  <body style=3D"        background-color: white;      ">
    <table style=3D"        background-color: white;         max-width: 548=
px;         color: black;         border-spacing: 0px 0px;         padding-=
top: 0px;         padding-bottom: 0px;        border-collapse: collapse;   =
   " width=3D"548" cellspacing=3D"0" cellpadding=3D"0">
      <tbody>
        <tr>
          <td style=3D"        text-align: left;        padding-bottom: 20p=
x;      ">
            <img height=3D"28" width=3D"126" style=3D"        max-width: 10=
0%;      " src=3D"https://products.office.com/en-us/CMSImages/Office365Logo=
_Orange.png?version=3Db8d100a9-0a8b-8e6a-88e1-ef488fee0470">
          </td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        font-size: 16px;         padding-bottom: 10px;         -ms=
-text-size-adjust: 100%;        text-align: left;      ">Your message to <s=
pan style=3D"        color: #0072c6;      ">sjkfjklsdjflkjlkjfdsljj@gmail.c=
om</span> couldn't be delivered.<br></td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;         font-size: 24px;         padding-top: 0px;         paddin=
g-bottom: 20px;         text-align: center;         -ms-text-size-adjust: 1=
00%;      ">
            <span style=3D"        color: #0072c6;      ">sjkfjklsdjflkjlkj=
fdsljj</span> wasn't found at <span style=3D"        color: #0072c6;      "=
>gmail.com</span>.<br></td>
        </tr>
        <tr>
          <td style=3D"        padding-bottom: 15px;         padding-left: =
0px;         padding-right: 0px;         border-spacing: 0px 0px;      ">
            <table style=3D"        max-width: 548px;         font-weight: =
600;        border-spacing: 0px 0px;         padding-top: 0px;         padd=
ing-bottom: 0px;        border-collapse: collapse;      ">
              <tbody>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        font-size: 15px;        font-weight: 600;        t=
ext-align: left;        width: 181px;        -ms-text-size-adjust: 100%;   =
     vertical-align: bottom;      ">
                    <font color=3D"#ffffff">
                      <span style=3D"color:#000000">kalpesh.vasave</span>
                    </font>
                  </td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        font-size: 15px;        font-weight: 600;        t=
ext-align: center;        width: 186px;        -ms-text-size-adjust: 100%; =
       vertical-align: bottom;      ">
                    <font color=3D"#ffffff">
                      <span style=3D"color:#000000">Office 365</span>
                    </font>
                  </td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;         -ms-text-size-adjust: 100%;         font-size: 15=
px;         font-weight: 600;        text-align: right;         width: 181p=
x;         vertical-align: bottom;      ">
                    <font color=3D"#ffffff">
                      <span style=3D"color:#000000">sjkfjklsdjflkjlkjfds. .=
 .</span>
                    </font>
                  </td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;        text-align: left;        padding-top: 0px=
;        padding-bottom: 0px;        vertical-align: middle;        width: =
181px;      ">
                    <font color=3D"#ffffff">
                      <span style=3D"        color: #c00000;      ">
                        <b>Action Required</b>
                      </span>
                    </font>
                  </td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;        text-align: center;        padding-top: 0=
px;        padding-bottom: 0px;        vertical-align: middle;        width=
: 186px;      ">
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;        text-align: right;        padding-top: 0p=
x;        padding-bottom: 0px;        vertical-align: middle;        width:=
 181px;      ">
                    <font color=3D"#ffffff">
                      <span style=3D"color:#000000">Recipient</span>
                    </font>
                  </td>
                </tr>
                <tr>
                  <td colspan=3D"3" style=3D"        padding-top:0;        =
padding-bottom:0;        padding-left:0;        padding-right:0      ">
                    <table cellspacing=3D"0" cellpadding=3D"0" style=3D"   =
     border-spacing: 0px 0px;        padding-top: 0px;        padding-botto=
m: 0px;        padding-left: 0px;        padding-right: 0px;        border-=
collapse: collapse;      ">
                      <tbody>
                        <tr height=3D"10">
                          <td width=3D"180" height=3D"10" bgcolor=3D"#c0000=
0" style=3D"        width: 180px;        line-height: 10px;        height: =
10px;        font-size: 6px;        padding-top: 0;        padding-bottom: =
0;        padding-left: 0;        padding-right: 0;      "><!--[if gte mso =
15]>&nbsp;<![endif]--></td>
                          <td width=3D"4" height=3D"10" bgcolor=3D"#ffffff"=
 style=3D"        width: 4px;        line-height: 10px;        height: 10px=
;        font-size: 6px;        padding-top: 0;        padding-bottom: 0;  =
      padding-left: 0;        padding-right: 0;      "><!--[if gte mso 15]>=
&nbsp;<![endif]--></td>
                          <td width=3D"180" height=3D"10" bgcolor=3D"#ccccc=
c" style=3D"        width: 180px;        line-height: 10px;        height: =
10px;        font-size: 6px;        padding-top: 0;        padding-bottom: =
0;        padding-left: 0;        padding-right: 0;      "><!--[if gte mso =
15]>&nbsp;<![endif]--></td>
                          <td width=3D"4" height=3D"10" bgcolor=3D"#ffffff"=
 style=3D"        width: 4px;        line-height: 10px;        height: 10px=
;        font-size: 6px;        padding-top: 0;        padding-bottom: 0;  =
      padding-left: 0;        padding-right: 0;      "><!--[if gte mso 15]>=
&nbsp;<![endif]--></td>
                          <td width=3D"180" height=3D"10" bgcolor=3D"#ccccc=
c" style=3D"        width: 180px;        line-height: 10px;        height: =
10px;        font-size: 6px;        padding-top: 0;        padding-bottom: =
0;        padding-left: 0;        padding-right: 0;      "><!--[if gte mso =
15]>&nbsp;<![endif]--></td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        text-align: left;        width: 181px;        line-height: 20px;  =
      font-weight: 400;        padding-top: 0px;        padding-left: 0px; =
       padding-right: 0px;        padding-bottom: 0px;      ">
                    <font color=3D"#ffffff">
                      <span style=3D"        color: #c00000;      ">Unknown=
 To address</span>
                    </font>
                  </td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        text-align: center;        width: 186px;        line-height: 20px;=
        font-weight: 400;        padding-top: 0px;        padding-left: 0px=
;        padding-right: 0px;        padding-bottom: 0px;      ">
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        text-align: right;        width: 181px;        line-height: 20px; =
       font-weight: 400;        padding-top: 0px;        padding-left: 0px;=
        padding-right: 0px;        padding-bottom: 0px;      ">
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td style=3D"        width: 100%;        padding-top: 0px;       =
 padding-right: 10px;        padding-left: 10px;      ">
            <br>
            <table style=3D"        width: 100%;        padding-right: 0px;=
        padding-left: 0px;        padding-top: 0px;        padding-bottom: =
0px;        background-color: #f2f5fa;        margin-left: 0px;      ">
              <tbody>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 21px=
;        font-weight: 500;        background-color: #f2f5fa;        padding=
-top: 0px;        padding-bottom: 0px;        padding-left: 10px;        pa=
dding-right: 10px;      ">How to Fix It</td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 16px=
;        font-weight: 400;        padding-top: 0px;        padding-bottom: =
6px;        padding-left: 10px;        padding-right: 10px;        backgrou=
nd-color: #f2f5fa;      ">The address may be misspelled or may not exist. T=
ry one or more of the following:</td>
                </tr>
                <tr>
                  <td style=3D"         padding-top: 0px;         padding-b=
ottom: 0px;         padding-left: 0px;         padding-right: 0px;        b=
order-spacing: 0px 0px;      ">
                    <ul style=3D"        font-family: 'Segoe UI', Frutiger,=
 Arial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 16=
px;        font-weight: 400;        margin-left: 40px;        margin-bottom=
: 5px;        background-color: #f2f5fa;        padding-top: 0px;        pa=
dding-bottom: 0px;        padding-left: 6px;        padding-right: 6px;    =
  ">
                      <li>Send the message again following these steps: In =
Outlook, open this non-delivery report (NDR) and choose <b>Send Again</b> f=
rom the Report ribbon. In Outlook on the web, select this NDR, then select =
the link &quot;<b>To send this message again, click here.</b>&quot; Then de=
lete and retype the entire recipient address. If prompted with an Auto-Comp=
lete List suggestion don't select it. After typing the complete address, cl=
ick <b>Send</b>.</li>
                      <li>Contact the recipient (by phone, for example) to =
check that the address exists and is correct.</li>
                      <li>The recipient may have set up email forwarding to=
 an incorrect address. Ask them to check that any forwarding they've set up=
 is working correctly.</li>
                      <li>Clear the recipient Auto-Complete List in Outlook=
 or Outlook on the web by following the steps in this article: <a href=3D"h=
ttps://go.microsoft.com/fwlink/?LinkId=3D389363">Fix email delivery issues =
for error code 5.1.1 in Office 365</a>, and then send the message again. Re=
type the entire recipient address before selecting <b>Send</b>.</li>
                    </ul>
                  </td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 16px=
;        font-weight: 400;        padding-top: 0px;        padding-bottom: =
6px;        padding-left: 10px;        padding-right: 10px;        backgrou=
nd-color: #f2f5fa;      ">If the problem continues, forward this message to=
 your email admin. If you're an email admin, refer to the <b>More Info for =
Email Admins</b> section below.</td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        -ms-text-size-adjust: 100%;        font-size: 14px;       =
 font-weight: 400;        padding-top: 10px;        padding-bottom: 0px;   =
     padding-bottom: 4px;      ">
            <br>
            <em>Was this helpful? <a href=3D"https://go.microsoft.com/fwlin=
k/?LinkId=3D525920">Send feedback to Microsoft</a>.</em>
          </td>
        </tr>
        <tr>
          <td style=3D"        -ms-text-size-adjust: 100%;        font-size=
: 0px;        line-height: 0px;        padding-top: 0px;        padding-bot=
tom: 0px;      ">
            <hr>
          </td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        -ms-text-size-adjust: 100%;        font-size: 21px;       =
 font-weight: 500;      ">
            <br>More Info for Email Admins</td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        -ms-text-size-adjust: 100%;        font-size: 14px;      "=
>
            <em>Status code: 550 5.1.1</em>
            <br>
            <br>This error occurs because the sender sent a message to an e=
mail address outside of Office 365, but the address is incorrect or doesn't=
 exist at the destination domain. The error is reported by the recipient do=
main's email server, but most often it must be fixed by the person who sent=
 the message. If the steps in the <b>How to Fix It</b> section above don't =
fix the problem, and you're the email admin for the recipient, try one or m=
ore of the following:<br><br><b>The email address exists and is correct</b>=
 - Confirm that the recipient address exists, is correct, and is accepting =
messages.<br><br><b>Synchronize your directories</b> - If you have a hybrid=
 environment and are using directory synchronization make sure the recipien=
t's email address is synced correctly in both Office 365 and in your on-pre=
mises directory.<br><br><b>Errant forwarding rule</b> - Check for forwardin=
g rules that aren't behaving as expected. Forwarding can be set up by an ad=
min via mail flow rules or mailbox forwarding address settings, or by the r=
ecipient via the Inbox Rules feature.<br><br><b>Mail flow settings and MX r=
ecords are not correct</b> - Misconfigured mail flow or MX record settings =
can cause this error. Check your Office 365 mail flow settings to make sure=
 your domain and any mail flow connectors are set up correctly. Also, work =
with your domain registrar to make sure the MX records for your domain are =
configured correctly.<br><br>For more information and additional tips to fi=
x this issue, see <a href=3D"https://go.microsoft.com/fwlink/?LinkId=3D3893=
63">Fix email delivery issues for error code 550 5.1.1 in Office 365</a>.<b=
r><br></td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        -ms-text-size-adjust: 100%;        font-size: 17px;       =
 font-weight: 500;      ">Original Message Details</td>
        </tr>
        <tr>
          <td style=3D"        font-size: 14px;        line-height: 20px;  =
      font-family: 'Segoe UI', Frutiger, Arial, sans-serif;        -ms-text=
-size-adjust: 100%;        font-weight: 500;      ">
            <table style=3D"        width: 100%;        border-collapse: co=
llapse;        margin-left: 10px;      ">
              <tbody>
                <tr>
                  <td valign=3D"top" style=3D"        font-family: 'Segoe U=
I', Frutiger, Arial, sans-serif;        font-size: 14px;        -ms-text-si=
ze-adjust: 100%;        white-space: nowrap;        font-weight: 500;      =
  width: 140px;      ">Created Date:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      ">26/08/2025 06:14:54</td>
                </tr>
                <tr>
                  <td valign=3D"top" style=3D"        font-family: 'Segoe U=
I', Frutiger, Arial, sans-serif;        font-size: 14px;        -ms-text-si=
ze-adjust: 100%;        white-space: nowrap;        font-weight: 500;      =
  width: 140px;      ">Sender Address:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      "><EMAIL></td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        font-size: 14px;        -ms-text-size-adjust: 100%=
;        white-space: nowrap;        font-weight: 500;        width: 140px;=
      ">Recipient Address:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      "><EMAIL></td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        font-size: 14px;        -ms-text-size-adjust: 100%=
;        white-space: nowrap;        font-weight: 500;        width: 140px;=
      ">Subject:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      ">attack on titan</td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        -ms-text-size-adjust: 100%;        font-size: 17px;       =
 font-weight: 500;      ">
            <br>Error Details</td>
        </tr>
        <tr>
          <td style=3D"        font-size: 14px;        line-height: 20px;  =
      font-family: 'Segoe UI', Frutiger, Arial, sans-serif;        -ms-text=
-size-adjust: 100%;        font-weight: 500;      ">
            <table style=3D"        width: 100%;        border-collapse: co=
llapse;        margin-left: 10px;      ">
              <tbody>
                <tr>
                  <td valign=3D"top" style=3D"        font-family: 'Segoe U=
I', Frutiger, Arial, sans-serif;        font-size: 14px;        -ms-text-si=
ze-adjust: 100%;        white-space: nowrap;        font-weight: 500;      =
  width: 140px;      ">Error:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      ">
                    <em>550-5.1.1 The email account that you tried to reach=
 does not exist. Please try
550-5.1.1 double-checking the recipient's email address for typos or
550-5.1.1 unnecessary spaces. For more information, go to
550 5.1.1  https://support.google.com/mail/?p=3DNoSuchUser 3f1490d57ef6-e96=
d57c7011si10********.535 - gsmtp</em>
                  </td>
                </tr>
                <tr>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        font-size: 14px;        -ms-text-size-adjust: 100%=
;        white-space: nowrap;        font-weight: 500;        width: 140px;=
      ">Message rejected by:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      ">mx.google.com</td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
        <tr>
          <td style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sa=
ns-serif;        -ms-text-size-adjust: 100%;        font-size: 17px;       =
 font-weight: 500;      ">
            <br>Notification Details</td>
        </tr>
        <tr>
          <td style=3D"        font-size: 14px;        line-height: 20px;  =
      font-family: 'Segoe UI', Frutiger, Arial, sans-serif;        -ms-text=
-size-adjust: 100%;        font-weight: 500;      ">
            <table style=3D"        width: 100%;        border-collapse: co=
llapse;        margin-left: 10px;      ">
              <tbody>
                <tr>
                  <td valign=3D"top" style=3D"        font-family: 'Segoe U=
I', Frutiger, Arial, sans-serif;        font-size: 14px;        -ms-text-si=
ze-adjust: 100%;        white-space: nowrap;        font-weight: 500;      =
  width: 140px;      ">Sent by:</td>
                  <td style=3D"        font-family: 'Segoe UI', Frutiger, A=
rial, sans-serif;        -ms-text-size-adjust: 100%;        font-size: 14px=
;        font-weight: 400;      ">
                    <em>MA0P287MB1322.INDP287.PROD.OUTLOOK.COM</em>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <br>
    <table style=3D"width: 880px;" cellspacing=3D"0">
      <tr>
        <td colspan=3D"6" style=3D"        padding-top: 4px;        border-=
bottom: 1px solid #999999;        padding-bottom: 4px;        line-height: =
120%;        font-size: 17px;        font-family: 'Segoe UI', Frutiger, Ari=
al, sans-serif;        -ms-text-size-adjust: 100%;        font-weight: 500;=
      ">Message Hops</td>
      </tr>
      <tr>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        background-color: #f2f5fa;        border-bottom: 1p=
x solid #999999;        white-space: nowrap;        padding: 8px;      ">HO=
P</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        background-color: #f2f5fa;        border-bottom: 1p=
x solid #999999;        white-space: nowrap;        padding: 8px;        wi=
dth: 80px;      ">TIME (UTC)</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        background-color: #f2f5fa;        border-bottom: 1p=
x solid #999999;        white-space: nowrap;        padding: 8px;      ">FR=
OM</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        background-color: #f2f5fa;        border-bottom: 1p=
x solid #999999;        white-space: nowrap;        padding: 8px;      ">TO=
</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        background-color: #f2f5fa;        border-bottom: 1p=
x solid #999999;        white-space: nowrap;        padding: 8px;      ">WI=
TH</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        background-color: #f2f5fa;        border-bottom: 1p=
x solid #999999;        white-space: nowrap;        padding: 8px;      ">RE=
LAY TIME</td>
      </tr>
      <tr>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: center;      ">1</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;        width: 80px;      ">26/08/2025<br>06:14=
:54</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">PN2P287MB1310.INDP287.PROD.OUTLOOK.COM<=
/td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">PN2P287MB1310.INDP287.PROD.OUTLOOK.COM<=
/td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">mapi</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">*</td>
      </tr>
      <tr>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: center;      ">2</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;        width: 80px;      ">26/08/2025<br>06:14=
:54</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">PN2P287MB1310.INDP287.PROD.OUTLOOK.COM<=
/td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">MA0P287MB1322.INDP287.PROD.OUTLOOK.COM<=
/td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">Microsoft SMTP Server (version=3DTLS1_2=
, cipher=3DTLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384)</td>
        <td style=3D"        font-size: 12px;        font-family: 'Segoe UI=
', Frutiger, Arial, sans-serif;        -ms-text-size-adjust: 100%;        f=
ont-weight: 500;        border-bottom: 1px solid #999999;        padding: 8=
px;        text-align: left;      ">*</td>
      </tr>
    </table>
    <p style=3D"        font-family: 'Segoe UI', Frutiger, Arial, sans-seri=
f;        -ms-text-size-adjust: 100%;        font-size: 17px;        font-w=
eight: 500;        padding-top: 4px;        padding-bottom: 0;        margi=
n-top: 19px;        margin-bottom: 5px;      ">Original Message Headers</p>
    <pre style=3D"        color: gray;        white-space: pre;        padd=
ing-top: 0;        margin-top: 5px;      ">ARC-Seal: i=3D1; a=3Drsa-sha256;=
 s=3Darcselector10001; d=3Dmicrosoft.com; cv=3Dnone;
 b=3DgKQOTBy2nX/Hc++VLkri3r3k1T4WdrfuOLdO1OjdQUU2e5Knlqp+xtOLj3KoyVhvLR+vkp=
+bb8vf5jL/JCi9LqijqJfw90YpNWAoZ8GFCm7kaYrYwXsLGrg6M0lpoqRJpBVAwiTvlOewhutmg=
q5ye5aQQV1l80svBNSLUkJBILYWubNBxe030SIU8AHpcfmMfET2HJQK3IJ6wkqo03Noyr2vpFC3=
mmmq57fa6iq8q5K4qf8poqwb6Td9/tKjTToGhZjAnpwKrp3MDTbyjJyBzq9C+MSbC1QfyZxkV/p=
bIBLcI8WX6E4VxPc0Th6/bCT/h9WNTc4v06eIyHE/aEsZaw=3D=3D
ARC-Message-Signature: i=3D1; a=3Drsa-sha256; c=3Drelaxed/relaxed; d=3Dmicr=
osoft.com;
 s=3Darcselector10001;
 h=3DFrom:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-A=
ntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Ex=
change-AntiSpam-MessageData-1;
 bh=3DqfR+mL8uA95rHBppsrvu5fQB/sVigMuxXmjkeyhyWPc=3D;
 b=3DN7W3qDbW6OXmRPkHt/BKHhaU67/7jkNWIOzbQJtBS1zVaMMR6isc7OO0dTzkrWznEg+etd=
HL0vOy9Glfct9DwB66l6uDxQb9dOplbDKbJEmyyXcl9Ix5I6qR4bZpEcOaUOl1ISRw6AUL2p3bY=
11/Kb9tnuMB0rrLl1nizDUO8/D/0+kv5v+yv/OSGlsRhgsOc4MThT250tBzGKVRUa3Hpmu35B3V=
H2B7boEnoWs1t43hpK2WsNEH8EpFr9SOk7nCSNM3TH0Jk5DnDSSHvhUW1ZMWJ93PDhKz0GWEcYQ=
DPcuDoqrScLTVFmHK2H4dGia31Jub2XNkyjmfPPLPl6v2Xw=3D=3D
ARC-Authentication-Results: i=3D1; mx.microsoft.com 1; spf=3Dpass
 smtp.mailfrom=3Dkylas.io; dmarc=3Dpass action=3Dnone header.from=3Dkylas.i=
o;
 dkim=3Dpass header.d=3Dkylas.io; arc=3Dnone
Received: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM (2603:1096:c01:1b3::1=
1)
 by MA0P287MB1322.INDP287.PROD.OUTLOOK.COM (2603:1096:a01:f4::6) with
 Microsoft SMTP Server (version=3DTLS1_2,
 cipher=3DTLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.9073.13; Tue, 26 =
Aug
 2025 06:14:54 +0000
Received: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
 ([fe80::2574:c107:e23a:1a6b]) by PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
 ([fe80::2574:c107:e23a:1a6b%6]) with mapi id 15.20.9052.019; Tue, 26 Aug 2=
025
 06:14:54 +0000
From: kalpesh vasave &lt;<EMAIL>&gt;
To: &quot;<EMAIL>&quot; &lt;sjkfjklsdjflkjlkjfdsl=
<EMAIL>&gt;
Subject: attack on titan
Thread-Topic: attack on titan
Thread-Index: AQHcFlC8rQ9iuP565kavRDjJHiAGTQ=3D=3D
Date: Tue, 26 Aug 2025 06:14:54 +0000
Message-ID: &lt;PN2P287MB13106C8F6EFBAEC248691958EE39A@PN2P287MB1310.INDP28=
7.PROD.OUTLOOK.COM&gt;
Accept-Language: en-GB, en-US
Content-Language: en-US
X-MS-Has-Attach:
X-MS-TNEF-Correlator:
authentication-results: dkim=3Dnone (message not signed)
 header.d=3Dnone;dmarc=3Dnone action=3Dnone header.from=3Dkylas.io;
x-ms-publictraffictype: Email
x-ms-traffictypediagnostic: PN2P287MB1310:EE_|MA0P287MB1322:EE_
x-ms-office365-filtering-correlation-id: 21c44dab-036d-493b-0dd9-08dde467df=
ad
x-ms-exchange-senderadcheck: 1
x-ms-exchange-antispam-relay: 0
x-microsoft-antispam: BCL:0;ARA:13230040|69100299015|376014|**********|3660=
16|38070700018|**********;
x-microsoft-antispam-message-info: =3D?us-ascii?Q?51YpMwBNwL+5qtUxbhXge+r5M=
fqrJciy7YNI32b2U7+BWz+iUSgsrjuY+tMa?=3D
 =3D?us-ascii?Q?0FBNuAmJy8z+eJe5Sc0K+04BuIyuEbfoGWpH3gQCg/kej7XIwCrPMeoo6Wh=
K?=3D
 =3D?us-ascii?Q?GnssFt8X/4KKtmyFYJpn5WidtQ/BLXlKmjhDZ99c3Saxn/GGFdFAk5cGvv5=
Z?=3D
 =3D?us-ascii?Q?duJaz22jQ3qTZUiP6HpiNNwUU8BliNu5ejukvPjj3Ma/D9oxYKxB6tRzcVH=
x?=3D
 =3D?us-ascii?Q?aPy/5yIDSWD+LtrhxYny6LcoqOazbsf4mQtJ/4YxUXu5tC61/CiwfNIMLPk=
u?=3D
 =3D?us-ascii?Q?CxeezzBvHFGUroKUXIxFz8Oz5WsD3wgh8EfIXwokLaTyZFMzH5VJip+nje1=
3?=3D
 =3D?us-ascii?Q?OLC+L+0yOZ/chgzHcFtKw1gqTjTgL+kSk2zvRVyr2hKy2HVpq3xJtMunQMx=
Y?=3D
 =3D?us-ascii?Q?icGaT+WBJZWFjEZO8e/Hn2F54o7ZT0y5DAB/R5q1qizDgj1FaPRnogC/cRU=
P?=3D
 =3D?us-ascii?Q?gsy1Oq1BoalRi57i5RIcw7J+cuC3J98weeUxfOuxh60s+G6m1ja1LVTEfWK=
2?=3D
 =3D?us-ascii?Q?IfRYfY/YrHqIR4LL5RaRNQTbzWj0IB/0zi2i2XlS5pPkXp7qxb6pzwKzahf=
v?=3D
 =3D?us-ascii?Q?gH3nAaTDAh2VRNotwPIMcl0mmZvVJWVQbxBihqjHbGVUEtwwEGsLeASbdYw=
H?=3D
 =3D?us-ascii?Q?3Ugdnwo47bFETm2DYwLkYfoqVk/1taqEDH9//BiWPtbMDFOQQPjrZpk4zeF=
c?=3D
 =3D?us-ascii?Q?ONY+1E7g7K3QHr8Yqz0AhZlDT3zRTsVT4gC5KwoP0FJgRpu8wcm7hh1X037=
K?=3D
 =3D?us-ascii?Q?7XWpnWfXE66jMXicrFvVQuGwra4gh9mfBPPQ27Sti6l39oZuGThfQlKIVLp=
K?=3D
 =3D?us-ascii?Q?UtWMkgGIJaY1GH23R0qsTuybt6O9Yq1z7wfReTg1LTrR0Ag9Yqlx8uKpw08=
y?=3D
 =3D?us-ascii?Q?72x83PNG/EmQtVdZy6zWh68+sOxn7xi8nYvhk6ZTnsRUBQFwAFT3kKdBZWV=
a?=3D
 =3D?us-ascii?Q?uzzVeCg1thsbFddp1NU9y8hIqKR7pwgx6EiViUn1KdgBr3oIFd8c4/hpL8y=
D?=3D
 =3D?us-ascii?Q?GEUQyPvtQSa8tKUzZFI6wIhwdzvFHmVL/QgYC15JxD+qY0PtZ5tr23ajwsF=
u?=3D
 =3D?us-ascii?Q?dEQ1HPd3hT4u8JSkXEHScpXTfeOkWgr7WcHpwsRZf6ifl1PgOEQOdcNlcVb=
E?=3D
 =3D?us-ascii?Q?QwkqaXQ02Pl/KluFj7c/88H7W9AELqYhzspApqRMMOSTjzmsPwKnHdp1sK5=
O?=3D
 =3D?us-ascii?Q?Ll/opGCbg8CNqT7ymWXL+GfK1Da6FwQygKeR50D6LA+vEvT3i4Npiyl6s4+=
1?=3D
 =3D?us-ascii?Q?gqawoC/y9aIZQpLdv6FGg2Gy5aoot43EwFC2XcCOWgDfHHOaNmp4Yj+11lg=
2?=3D
 =3D?us-ascii?Q?JCp5kMeJmwY5llh0SSsj+I4K+Pln9se3tEBHRLnZ5Mgq9Y3NDrJFJYEnzVU=
C?=3D
 =3D?us-ascii?Q?sYyuA2FAwQoyjwbQWUCfGqu/45DWtjDucMBPbtGIwtUx03XLKt3wBAIBiQO=
o?=3D
 =3D?us-ascii?Q?q8TxaPPULFc=3D3D?=3D
x-forefront-antispam-report: CIP:***************;CTRY:;LANG:en;SCL:1;SRV:;I=
PV:NLI;SFV:NSPM;H:PN2P287MB1310.INDP287.PROD.OUTLOOK.COM;PTR:;CAT:NONE;SFS:=
(13230040)(69100299015)(376014)(**********)(366016)(38070700018)(**********=
);DIR:OUT;SFP:1102;
x-ms-exchange-antispam-messagedata-chunkcount: 1
x-ms-exchange-antispam-messagedata-0: =3D?us-ascii?Q?3Nac/ywb9flnIun6pnhAQw=
M7SKHV5VM9niOdw8+lIqmUzoIb+nTaiJKZwTJB?=3D
 =3D?us-ascii?Q?dTfgTegnMtdsEv/GHXh+uM7cUzEAe01YgoOuOF94DoFWD1U++eGEfn/1t76=
D?=3D
 =3D?us-ascii?Q?veeizzErzW0dbZYLmN7MkyXEcpRPROzqIwEudRpHw5KqoQY7GJhTvPqHb06=
3?=3D
 =3D?us-ascii?Q?R6ouZkSeiWNBrr4yIVSVsT6ROuRKkhBIcw6vEhH+WYJkVdU1X0Qf3k8Nrp6=
/?=3D
 =3D?us-ascii?Q?TcmBFkjEnOVlLUuXFaHo7GAHp02Gfdw80BPKbj815A6ntu/Wcz6zBKZanjp=
T?=3D
 =3D?us-ascii?Q?9RetJRPrVFo/pguXfrGM4XpPWnPBuFw5QJ/WhYddKJ6GezPivyyr5d8OHsN=
n?=3D
 =3D?us-ascii?Q?4xZpH74IhUOZW/LjhwZ1OR6FpwHnmXgz/asrxjdT5a4C3QJMvHc8SZkI3x8=
2?=3D
 =3D?us-ascii?Q?Q/hEji/puaX60+TcrmFKkxPqfCPc4aO4+IUi/vyb7wdgEh8ixnQVR1Hs1Xd=
l?=3D
 =3D?us-ascii?Q?tPFDIrKk4kBkrDaGYHmcO9LuXkNRSoQX9OSpHGazlP+4waRY5IktYt3Md3A=
V?=3D
 =3D?us-ascii?Q?UzohMer6oBBNk2XpAb72/kmOgJdvqKCTQSZs2nRuzKVaavEYizNwMeL8OK5=
T?=3D
 =3D?us-ascii?Q?LBFRG9RwSnXcytu37bBuCaOjPdbRtNlF80bi90snOe2EXZzzMCOe2dfi5Uy=
i?=3D
 =3D?us-ascii?Q?TAnOiIdvNVXxlcrMedHJbj7h+20ETbKZpiQsffz/gJafMppjcXt4AJ8n0NJ=
P?=3D
 =3D?us-ascii?Q?R/7NBA9WfVJcHpaFFJUciNPSpDLyTkWAcQ0gQgmMdvHrLYlFQ8gwx3sNIBT=
p?=3D
 =3D?us-ascii?Q?6JFVM0uRvjV+mamS9bA48GuotfPuOlzzOSgixPkmgZZKzNd6zD/LyxkLrJO=
0?=3D
 =3D?us-ascii?Q?914ZaAzvk57CZW8w4GO1WPgrVkpUcTSRu7PuctU/apPlks+OX4oI3rHllJP=
w?=3D
 =3D?us-ascii?Q?IPbYVdEvoBs1RGWAYpCAdxno25k1T+pwsMGP0aOMVUny/Q6ffZaKoikc6xY=
U?=3D
 =3D?us-ascii?Q?S2Z1x4h7T2gCmDwHLbxkYjqANEYlkS+f6SqP/NtV/29pnELj5yxmyrUTBKW=
G?=3D
 =3D?us-ascii?Q?uaI79iklKfU5G03gJQ+6TihDyuq5S8c2XHFHEco1GVgxTQza6/xy4qxakBB=
3?=3D
 =3D?us-ascii?Q?po2Uq4WoVtIg1OgSCFod/1bxlDLId7AUmhUj08Dv1zNAGuoYC0gHD/BjxDy=
O?=3D
 =3D?us-ascii?Q?zIFya4JHqkiJM35RBSI5gV0le6I4ettCdhhesGSjedARXwPepyEDLESIUdh=
l?=3D
 =3D?us-ascii?Q?Y7v+woBz0eFkKg2+zpyzcv7jr73ZkeJVZVvnyfobixaAGTm8fmjQpj376XJ=
f?=3D
 =3D?us-ascii?Q?kD5qTZEN0AnmwELXdGEj4lRgqefmCdz/icc6Smfd9hnc8BwWgBJGjJAArqu=
f?=3D
 =3D?us-ascii?Q?pGcuMvDsRTqFWx4WND1J1sIAPrbqS+mJ3TqUQ9XFPciElsp6zS0DzcTouAw=
Q?=3D
 =3D?us-ascii?Q?0pd9y5EJyGfJDRhlDwlcWsqo5lhK5ZkFwqqdQT1gHFx/3XpVrW5vIFIYPS+=
K?=3D
 =3D?us-ascii?Q?pXBKhn015KWAAcIKfmCKBvhUJrdXCykSPE154ouR3288SV1nPA7jJhnG2Dj=
R?=3D
 =3D?us-ascii?Q?bnWLm/h2ghKvNfIjHMY1JdYrow3ofPaPS8xebWGEFIMuKsV8pXFo0+yVLYn=
o?=3D
 =3D?us-ascii?Q?pI+9IhPmBhPQ3iiWaIfb+8JjBNnYnfakKQVh38jd?=3D
Content-Type: multipart/alternative;
	boundary=3D&quot;_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB1310I=
NDP_&quot;
MIME-Version: 1.0
X-OriginatorOrg: kylas.io
X-MS-Exchange-CrossTenant-AuthAs: Internal
X-MS-Exchange-CrossTenant-AuthSource: PN2P287MB1310.INDP287.PROD.OUTLOOK.CO=
M
X-MS-Exchange-CrossTenant-Network-Message-Id: 21c44dab-036d-493b-0dd9-08dde=
467dfad
X-MS-Exchange-CrossTenant-originalarrivaltime: 26 Aug 2025 06:14:54.1502
 (UTC)
X-MS-Exchange-CrossTenant-fromentityheader: Hosted
X-MS-Exchange-CrossTenant-id: f0dce677-c472-4176-a936-4eb93f005cf5
X-MS-Exchange-CrossTenant-mailboxtype: HOSTED
X-MS-Exchange-CrossTenant-userprincipalname: lEdq7XZgdA4yEyHcA6eodsA1CUUfXt=
mPBcewMnVATUZXgXkSeQ0jMyTqOpAGTjmrgYecWzAE9zsf1avKlf6SYQ=3D=3D
X-MS-Exchange-Transport-CrossTenantHeadersStamped: MA0P287MB1322
</pre>
  </body>
</html>=

--b5c604e1-ba92-4836-b248-2a13725dc494--

--1a585fe9-3568-4b04-8bdb-74ca6d1d8f95
Content-Type: message/delivery-status

Reporting-MTA: dns;MA0P287MB1322.INDP287.PROD.OUTLOOK.COM
Received-From-MTA: dns;PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
Arrival-Date: Tue, 26 Aug 2025 06:14:54 +0000

This is a delivery status notification for a temporary failure.

Action: failed
Status: 4.2.1
Diagnostic-Code: smtp;421-4.2.1 Service temporarily unavailable
Remote-MTA: dns;mx.google.com
X-Display-Name: <EMAIL>


--1a585fe9-3568-4b04-8bdb-74ca6d1d8f95
Content-Type: message/rfc822

ARC-Seal: i=1; a=rsa-sha256; s=arcselector10001; d=microsoft.com; cv=none;
 b=gKQOTBy2nX/Hc++VLkri3r3k1T4WdrfuOLdO1OjdQUU2e5Knlqp+xtOLj3KoyVhvLR+vkp+bb8vf5jL/JCi9LqijqJfw90YpNWAoZ8GFCm7kaYrYwXsLGrg6M0lpoqRJpBVAwiTvlOewhutmgq5ye5aQQV1l80svBNSLUkJBILYWubNBxe030SIU8AHpcfmMfET2HJQK3IJ6wkqo03Noyr2vpFC3mmmq57fa6iq8q5K4qf8poqwb6Td9/tKjTToGhZjAnpwKrp3MDTbyjJyBzq9C+MSbC1QfyZxkV/pbIBLcI8WX6E4VxPc0Th6/bCT/h9WNTc4v06eIyHE/aEsZaw==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com;
 s=arcselector10001;
 h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-AntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Exchange-AntiSpam-MessageData-1;
 bh=qfR+mL8uA95rHBppsrvu5fQB/sVigMuxXmjkeyhyWPc=;
 b=N7W3qDbW6OXmRPkHt/BKHhaU67/7jkNWIOzbQJtBS1zVaMMR6isc7OO0dTzkrWznEg+etdHL0vOy9Glfct9DwB66l6uDxQb9dOplbDKbJEmyyXcl9Ix5I6qR4bZpEcOaUOl1ISRw6AUL2p3bY11/Kb9tnuMB0rrLl1nizDUO8/D/0+kv5v+yv/OSGlsRhgsOc4MThT250tBzGKVRUa3Hpmu35B3VH2B7boEnoWs1t43hpK2WsNEH8EpFr9SOk7nCSNM3TH0Jk5DnDSSHvhUW1ZMWJ93PDhKz0GWEcYQDPcuDoqrScLTVFmHK2H4dGia31Jub2XNkyjmfPPLPl6v2Xw==
ARC-Authentication-Results: i=1; mx.microsoft.com 1; spf=pass
 smtp.mailfrom=kylas.io; dmarc=pass action=none header.from=kylas.io;
 dkim=pass header.d=kylas.io; arc=none
Received: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM (2603:1096:c01:1b3::11)
 by MA0P287MB1322.INDP287.PROD.OUTLOOK.COM (2603:1096:a01:f4::6) with
 Microsoft SMTP Server (version=TLS1_2,
 cipher=TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384) id 15.20.9073.13; Tue, 26 Aug
 2025 06:14:54 +0000
Received: from PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
 ([fe80::2574:c107:e23a:1a6b]) by PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
 ([fe80::2574:c107:e23a:1a6b%6]) with mapi id 15.20.9052.019; Tue, 26 Aug 2025
 06:14:54 +0000
From: kalpesh vasave <<EMAIL>>
To: "<EMAIL>" <<EMAIL>>
Subject: attack on titan
Thread-Topic: attack on titan
Thread-Index: AQHcFlC8rQ9iuP565kavRDjJHiAGTQ==
Date: Tue, 26 Aug 2025 06:14:54 +0000
Message-ID: <<EMAIL>>
Accept-Language: en-GB, en-US
Content-Language: en-US
X-MS-Has-Attach:
X-MS-TNEF-Correlator:
authentication-results: dkim=none (message not signed)
 header.d=none;dmarc=none action=none header.from=kylas.io;
x-ms-publictraffictype: Email
x-ms-traffictypediagnostic: PN2P287MB1310:EE_|MA0P287MB1322:EE_
x-ms-office365-filtering-correlation-id: 21c44dab-036d-493b-0dd9-08dde467dfad
x-ms-exchange-senderadcheck: 1
x-ms-exchange-antispam-relay: 0
x-microsoft-antispam: BCL:0;ARA:13230040|69100299015|376014|**********|366016|38070700018|**********;
x-microsoft-antispam-message-info: =?us-ascii?Q?51YpMwBNwL+5qtUxbhXge+r5MfqrJciy7YNI32b2U7+BWz+iUSgsrjuY+tMa?=
 =?us-ascii?Q?0FBNuAmJy8z+eJe5Sc0K+04BuIyuEbfoGWpH3gQCg/kej7XIwCrPMeoo6WhK?=
 =?us-ascii?Q?GnssFt8X/4KKtmyFYJpn5WidtQ/BLXlKmjhDZ99c3Saxn/GGFdFAk5cGvv5Z?=
 =?us-ascii?Q?duJaz22jQ3qTZUiP6HpiNNwUU8BliNu5ejukvPjj3Ma/D9oxYKxB6tRzcVHx?=
 =?us-ascii?Q?aPy/5yIDSWD+LtrhxYny6LcoqOazbsf4mQtJ/4YxUXu5tC61/CiwfNIMLPku?=
 =?us-ascii?Q?CxeezzBvHFGUroKUXIxFz8Oz5WsD3wgh8EfIXwokLaTyZFMzH5VJip+nje13?=
 =?us-ascii?Q?OLC+L+0yOZ/chgzHcFtKw1gqTjTgL+kSk2zvRVyr2hKy2HVpq3xJtMunQMxY?=
 =?us-ascii?Q?icGaT+WBJZWFjEZO8e/Hn2F54o7ZT0y5DAB/R5q1qizDgj1FaPRnogC/cRUP?=
 =?us-ascii?Q?gsy1Oq1BoalRi57i5RIcw7J+cuC3J98weeUxfOuxh60s+G6m1ja1LVTEfWK2?=
 =?us-ascii?Q?IfRYfY/YrHqIR4LL5RaRNQTbzWj0IB/0zi2i2XlS5pPkXp7qxb6pzwKzahfv?=
 =?us-ascii?Q?gH3nAaTDAh2VRNotwPIMcl0mmZvVJWVQbxBihqjHbGVUEtwwEGsLeASbdYwH?=
 =?us-ascii?Q?3Ugdnwo47bFETm2DYwLkYfoqVk/1taqEDH9//BiWPtbMDFOQQPjrZpk4zeFc?=
 =?us-ascii?Q?ONY+1E7g7K3QHr8Yqz0AhZlDT3zRTsVT4gC5KwoP0FJgRpu8wcm7hh1X037K?=
 =?us-ascii?Q?7XWpnWfXE66jMXicrFvVQuGwra4gh9mfBPPQ27Sti6l39oZuGThfQlKIVLpK?=
 =?us-ascii?Q?UtWMkgGIJaY1GH23R0qsTuybt6O9Yq1z7wfReTg1LTrR0Ag9Yqlx8uKpw08y?=
 =?us-ascii?Q?72x83PNG/EmQtVdZy6zWh68+sOxn7xi8nYvhk6ZTnsRUBQFwAFT3kKdBZWVa?=
 =?us-ascii?Q?uzzVeCg1thsbFddp1NU9y8hIqKR7pwgx6EiViUn1KdgBr3oIFd8c4/hpL8yD?=
 =?us-ascii?Q?GEUQyPvtQSa8tKUzZFI6wIhwdzvFHmVL/QgYC15JxD+qY0PtZ5tr23ajwsFu?=
 =?us-ascii?Q?dEQ1HPd3hT4u8JSkXEHScpXTfeOkWgr7WcHpwsRZf6ifl1PgOEQOdcNlcVbE?=
 =?us-ascii?Q?QwkqaXQ02Pl/KluFj7c/88H7W9AELqYhzspApqRMMOSTjzmsPwKnHdp1sK5O?=
 =?us-ascii?Q?Ll/opGCbg8CNqT7ymWXL+GfK1Da6FwQygKeR50D6LA+vEvT3i4Npiyl6s4+1?=
 =?us-ascii?Q?gqawoC/y9aIZQpLdv6FGg2Gy5aoot43EwFC2XcCOWgDfHHOaNmp4Yj+11lg2?=
 =?us-ascii?Q?JCp5kMeJmwY5llh0SSsj+I4K+Pln9se3tEBHRLnZ5Mgq9Y3NDrJFJYEnzVUC?=
 =?us-ascii?Q?sYyuA2FAwQoyjwbQWUCfGqu/45DWtjDucMBPbtGIwtUx03XLKt3wBAIBiQOo?=
 =?us-ascii?Q?q8TxaPPULFc=3D?=
x-forefront-antispam-report: CIP:***************;CTRY:;LANG:en;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:PN2P287MB1310.INDP287.PROD.OUTLOOK.COM;PTR:;CAT:NONE;SFS:(13230040)(69100299015)(376014)(**********)(366016)(38070700018)(**********);DIR:OUT;SFP:1102;
x-ms-exchange-antispam-messagedata-chunkcount: 1
x-ms-exchange-antispam-messagedata-0: =?us-ascii?Q?3Nac/ywb9flnIun6pnhAQwM7SKHV5VM9niOdw8+lIqmUzoIb+nTaiJKZwTJB?=
 =?us-ascii?Q?dTfgTegnMtdsEv/GHXh+uM7cUzEAe01YgoOuOF94DoFWD1U++eGEfn/1t76D?=
 =?us-ascii?Q?veeizzErzW0dbZYLmN7MkyXEcpRPROzqIwEudRpHw5KqoQY7GJhTvPqHb063?=
 =?us-ascii?Q?R6ouZkSeiWNBrr4yIVSVsT6ROuRKkhBIcw6vEhH+WYJkVdU1X0Qf3k8Nrp6/?=
 =?us-ascii?Q?TcmBFkjEnOVlLUuXFaHo7GAHp02Gfdw80BPKbj815A6ntu/Wcz6zBKZanjpT?=
 =?us-ascii?Q?9RetJRPrVFo/pguXfrGM4XpPWnPBuFw5QJ/WhYddKJ6GezPivyyr5d8OHsNn?=
 =?us-ascii?Q?4xZpH74IhUOZW/LjhwZ1OR6FpwHnmXgz/asrxjdT5a4C3QJMvHc8SZkI3x82?=
 =?us-ascii?Q?Q/hEji/puaX60+TcrmFKkxPqfCPc4aO4+IUi/vyb7wdgEh8ixnQVR1Hs1Xdl?=
 =?us-ascii?Q?tPFDIrKk4kBkrDaGYHmcO9LuXkNRSoQX9OSpHGazlP+4waRY5IktYt3Md3AV?=
 =?us-ascii?Q?UzohMer6oBBNk2XpAb72/kmOgJdvqKCTQSZs2nRuzKVaavEYizNwMeL8OK5T?=
 =?us-ascii?Q?LBFRG9RwSnXcytu37bBuCaOjPdbRtNlF80bi90snOe2EXZzzMCOe2dfi5Uyi?=
 =?us-ascii?Q?TAnOiIdvNVXxlcrMedHJbj7h+20ETbKZpiQsffz/gJafMppjcXt4AJ8n0NJP?=
 =?us-ascii?Q?R/7NBA9WfVJcHpaFFJUciNPSpDLyTkWAcQ0gQgmMdvHrLYlFQ8gwx3sNIBTp?=
 =?us-ascii?Q?6JFVM0uRvjV+mamS9bA48GuotfPuOlzzOSgixPkmgZZKzNd6zD/LyxkLrJO0?=
 =?us-ascii?Q?914ZaAzvk57CZW8w4GO1WPgrVkpUcTSRu7PuctU/apPlks+OX4oI3rHllJPw?=
 =?us-ascii?Q?IPbYVdEvoBs1RGWAYpCAdxno25k1T+pwsMGP0aOMVUny/Q6ffZaKoikc6xYU?=
 =?us-ascii?Q?S2Z1x4h7T2gCmDwHLbxkYjqANEYlkS+f6SqP/NtV/29pnELj5yxmyrUTBKWG?=
 =?us-ascii?Q?uaI79iklKfU5G03gJQ+6TihDyuq5S8c2XHFHEco1GVgxTQza6/xy4qxakBB3?=
 =?us-ascii?Q?po2Uq4WoVtIg1OgSCFod/1bxlDLId7AUmhUj08Dv1zNAGuoYC0gHD/BjxDyO?=
 =?us-ascii?Q?zIFya4JHqkiJM35RBSI5gV0le6I4ettCdhhesGSjedARXwPepyEDLESIUdhl?=
 =?us-ascii?Q?Y7v+woBz0eFkKg2+zpyzcv7jr73ZkeJVZVvnyfobixaAGTm8fmjQpj376XJf?=
 =?us-ascii?Q?kD5qTZEN0AnmwELXdGEj4lRgqefmCdz/icc6Smfd9hnc8BwWgBJGjJAArquf?=
 =?us-ascii?Q?pGcuMvDsRTqFWx4WND1J1sIAPrbqS+mJ3TqUQ9XFPciElsp6zS0DzcTouAwQ?=
 =?us-ascii?Q?0pd9y5EJyGfJDRhlDwlcWsqo5lhK5ZkFwqqdQT1gHFx/3XpVrW5vIFIYPS+K?=
 =?us-ascii?Q?pXBKhn015KWAAcIKfmCKBvhUJrdXCykSPE154ouR3288SV1nPA7jJhnG2DjR?=
 =?us-ascii?Q?bnWLm/h2ghKvNfIjHMY1JdYrow3ofPaPS8xebWGEFIMuKsV8pXFo0+yVLYno?=
 =?us-ascii?Q?pI+9IhPmBhPQ3iiWaIfb+8JjBNnYnfakKQVh38jd?=
Content-Type: multipart/alternative;
	boundary="_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB1310INDP_"
X-OriginatorOrg: kylas.io
X-MS-Exchange-CrossTenant-AuthAs: Internal
X-MS-Exchange-CrossTenant-AuthSource: PN2P287MB1310.INDP287.PROD.OUTLOOK.COM
X-MS-Exchange-CrossTenant-Network-Message-Id: 21c44dab-036d-493b-0dd9-08dde467dfad
X-MS-Exchange-CrossTenant-originalarrivaltime: 26 Aug 2025 06:14:54.1502
 (UTC)
X-MS-Exchange-CrossTenant-fromentityheader: Hosted
X-MS-Exchange-CrossTenant-id: f0dce677-c472-4176-a936-4eb93f005cf5
X-MS-Exchange-CrossTenant-mailboxtype: HOSTED
X-MS-Exchange-CrossTenant-userprincipalname: lEdq7XZgdA4yEyHcA6eodsA1CUUfXtmPBcewMnVATUZXgXkSeQ0jMyTqOpAGTjmrgYecWzAE9zsf1avKlf6SYQ==
X-MS-Exchange-Transport-CrossTenantHeadersStamped: MA0P287MB1322
MIME-Version: 1.0

--_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB1310INDP_
Content-Type: text/plain; charset="us-ascii"
Content-Transfer-Encoding: quoted-printable

attach on titan



--





--_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB1310INDP_
Content-Type: text/html; charset="us-ascii"
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w=
3.org/TR/REC-html40/loose.dtd"><html><head>
<meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dus-ascii"=
>
</head>
<body>
<p style=3D"margin: 0;">attach on titan</p>
<p style=3D"margin: 0;">&nbsp;</p>
<p style=3D"margin: 0;">--</p>
<p style=3D"margin: 0;">&nbsp;</p>
<img width=3D"1" height=3D"1" id=3D"img_05b324f0" style=3D"display: none; w=
idth: 1; height: 1; border: 0" src=3D"https://api-qa.sling-dev.com/v1/email=
_mappings/17005c4b-9d29-4667-a214-77d0cd537d4a?cache_buster=3D1756188892">
</body>
</html>

--_000_PN2P287MB13106C8F6EFBAEC248691958EE39APN2P287MB1310INDP_--

--1a585fe9-3568-4b04-8bdb-74ca6d1d8f95--