# frozen_string_literal: true

class RelativeFilters
  def initialize(field, timezone, value = nil)
    @field = field
    @timezone = timezone.blank? ? DEFAULT_IST_TIMEZONE : timezone
    @value = value
  end

  def today
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_day.utc,
      Time.now.in_time_zone(@timezone).end_of_day.utc
    )
  end

  def tomorrow
    build_between_query(
      Time.now.in_time_zone(@timezone).tomorrow.beginning_of_day.utc,
      Time.now.in_time_zone(@timezone).tomorrow.end_of_day.utc
    )
  end

  def yesterday
    build_between_query(
      Time.now.in_time_zone(@timezone).yesterday.beginning_of_day.utc,
      Time.now.in_time_zone(@timezone).yesterday.end_of_day.utc
    )
  end

  def last_seven_days
    build_between_query(
      (Time.now.in_time_zone(@timezone) - 7.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) - 1.days).end_of_day.utc
    )
  end

  def next_seven_days
    build_between_query(
      (Time.now.in_time_zone(@timezone) + 1.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) + 7.days).end_of_day.utc
    )
  end

  def last_fifteen_days
    build_between_query(
      (Time.now.in_time_zone(@timezone) - 15.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) - 1.days).end_of_day.utc
    )
  end

  def next_fifteen_days
    build_between_query(
      (Time.now.in_time_zone(@timezone) + 1.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) + 15.days).end_of_day.utc
    )
  end

  def last_thirty_days
    build_between_query(
      (Time.now.in_time_zone(@timezone) - 30.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) - 1.days).end_of_day.utc
    )
  end

  def next_thirty_days
    build_between_query(
      (Time.now.in_time_zone(@timezone) + 1.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) + 30.days).end_of_day.utc
    )
  end

  def current_week
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_week.utc,
      Time.now.in_time_zone(@timezone).end_of_week.utc
    )
  end

  def last_week
    build_between_query(
      Time.now.in_time_zone(@timezone).last_week.beginning_of_week.utc,
      Time.now.in_time_zone(@timezone).last_week.end_of_week.utc
    )
  end

  def next_week
    build_between_query(
      Time.now.in_time_zone(@timezone).next_week.beginning_of_week.utc,
      Time.now.in_time_zone(@timezone).next_week.end_of_week.utc
    )
  end

  def current_month
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_month.utc,
      Time.now.in_time_zone(@timezone).end_of_month.utc
    )
  end

  def last_month
    build_between_query(
      Time.now.in_time_zone(@timezone).last_month.beginning_of_month.utc,
      Time.now.in_time_zone(@timezone).last_month.end_of_month.utc
    )
  end

  def next_month
    build_between_query(
      Time.now.in_time_zone(@timezone).next_month.beginning_of_month.utc,
      Time.now.in_time_zone(@timezone).next_month.end_of_month.utc
    )
  end

  def current_quarter
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_quarter.utc,
      Time.now.in_time_zone(@timezone).end_of_quarter.utc
    )
  end

  def last_quarter
    build_between_query(
      Time.now.in_time_zone(@timezone).last_quarter.beginning_of_quarter.utc,
      Time.now.in_time_zone(@timezone).last_quarter.end_of_quarter.utc
    )
  end

  def next_quarter
    build_between_query(
      Time.now.in_time_zone(@timezone).next_quarter.beginning_of_quarter.utc,
      Time.now.in_time_zone(@timezone).next_quarter.end_of_quarter.utc
    )
  end

  def current_year
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_year.utc,
      Time.now.in_time_zone(@timezone).end_of_year.utc
    )
  end

  def last_year
    build_between_query(
      Time.now.in_time_zone(@timezone).last_year.beginning_of_year.utc,
      Time.now.in_time_zone(@timezone).last_year.end_of_year.utc
    )
  end

  def next_year
    build_between_query(
      Time.now.in_time_zone(@timezone).next_year.beginning_of_year.utc,
      Time.now.in_time_zone(@timezone).next_year.end_of_year.utc
    )
  end

  def week_to_date
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_week.utc,
      Time.now.in_time_zone(@timezone).end_of_day.utc
    )
  end

  def month_to_date
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_month.utc,
      Time.now.in_time_zone(@timezone).end_of_day.utc
    )
  end

  def quarter_to_date
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_quarter.utc,
      Time.now.in_time_zone(@timezone).end_of_day.utc
    )
  end

  def year_to_date
    build_between_query(
      Time.now.in_time_zone(@timezone).beginning_of_year.utc,
      Time.now.in_time_zone(@timezone).end_of_day.utc
    )
  end

  def before_current_date_and_time
    "(#{@field} < '#{format_date_time(Time.zone.now)}')"
  end

  def after_current_date_and_time
    "(#{@field} > '#{format_date_time(Time.zone.now)}')"
  end

  def last_n_days
    n = @value.to_i
    build_between_query(
      (Time.now.in_time_zone(@timezone) - n.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) - 1.days).end_of_day.utc
    )
  end

  def next_n_days
    n = @value.to_i
    build_between_query(
      (Time.now.in_time_zone(@timezone) + 1.days).beginning_of_day.utc,
      (Time.now.in_time_zone(@timezone) + n.days).end_of_day.utc
    )
  end

  private

  def format_date_time(value)
    value.iso8601(6)
  end

  def build_between_query(start_date, end_date)
    "(#{@field} between '#{format_date_time(start_date)}' and '#{format_date_time(end_date)}')"
  end
end
