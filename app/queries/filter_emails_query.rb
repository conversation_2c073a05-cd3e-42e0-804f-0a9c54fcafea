class FilterEmailsQuery
  prepend SimpleCommand

  OPERATOR_MAPPING = {
    'equal' => '=',
    'not_equal' => '!=',
    'greater' => '>',
    'less' => '<',
    'greater_or_equal' => '>=',
    'less_or_equal' => '<=',
    'between' => 'between',
    'not_between' => 'not between',
    'in' => 'in',
    'not_in' => 'not in',
    'is_null' => 'is null',
    'is_not_null' => 'is not null',
    'is_empty' => 'is null',
    'is_not_empty' => 'is not null',
    'contains' => 'ilike',
    'not_contains' => 'not ilike',
    'begins_with' => 'ilike',
    'multi_field' => 'ilike'
  }.freeze

  def initialize(auth_data, filter_params)
    @auth_data = auth_data
    @filter_params = filter_params
    @look_up_join_entities = []
    @field_wise_filters = YAML.safe_load(File.read(File.join(File.dirname(__FILE__), 'field-wise-operators.yml')))
    @filterable_fields =
      JSON.parse(File.read("#{Rails.root}/app/files/email-list-layout.json"))['pageConfig']['tableConfig']['columns']
      .select { |field| field['isFilterable'] }.map { |field| field['id'] }
    @filterable_fields += %w[multi_field related_to email_thread_id id direction]
    @inner_join_sender_relation = false
    @inner_join_connected_account_relation = false
    @left_outer_join_attachments_relation = false
    @left_outer_join_read_unread_relation = false
  end

  def call
    sql = emails_for_tenant
    Rails.logger.info "SQL: #{sql}"
    scope = paginate(sql)
    scope
  end

  private

  def emails_for_tenant

    processed_conditions = "emails.tenant_id = #{@auth_data.tenant_id}"

    unless @auth_data.can_read_all_emails?
      look_up_joins =  "LEFT OUTER JOIN email_look_ups all_elu ON emails.id = all_elu.email_id AND all_elu.tenant_id = #{@auth_data.tenant_id} AND all_elu.deleted = FALSE
          LEFT OUTER JOIN look_ups all_lu ON all_elu.look_up_id = all_lu.id AND all_lu.deleted = FALSE"

    end
    processed_conditions = processed_conditions + " and " + conditions 
    query = <<~END_SQL
      SELECT * FROM (
        SELECT
          emails.id,
          emails.created_at,
          ROW_NUMBER()
          OVER (
            PARTITION BY emails.email_thread_id
            ORDER BY emails.#{sorting_order}
          ) as rownum
          FROM emails
          #{inner_join_sender}
          #{entity_joins}
          #{inner_join_connected_account}
          #{left_outer_join_attachments}
          #{left_outer_join_read_unread}
          #{@left_outer_join_opened_at_relation.presence}
          #{@left_outer_join_clicked_at_relation.presence}
          #{look_up_joins.presence}
          LEFT OUTER JOIN email_threads ON emails.email_thread_id = email_threads.id
          WHERE #{processed_conditions} and
          emails.deleted = false
        ) tmp
        WHERE rownum=1
        ORDER BY tmp.#{sorting_order}
    END_SQL

    # this is temporary for video
=begin
    connected_account = User.find(@auth_data.user_id).connected_accounts.where(active: true).last
    if connected_account
      scope = Email.left_outer_joins(:related_to)
        .where(tenant_id: @tenant_id)
        .select("DISTINCT emails.id, email_thread_id, emails.sender_id, emails.subject, emails.body, emails.created_at, emails.read_by")
      thread_ids = EmailThread.where(id: scope.collect(&:email_thread_id)).pluck(:source_thread_id)
      GetGoogleThreads.call(thread_ids.compact, connected_account) if thread_ids
    end
=end
    query
  end

  def sorting_order
    field = @filter_params[:sort]&.split(',')&.first&.underscore || 'created_at'
    order = @filter_params[:sort]&.split(',')&.last&.upcase || 'DESC'

    if field == 'sent_at'
      field = 'created_at'
    end

    if field != 'created_at' || %w[ASC DESC].exclude?(order)
      Rails.logger.error "Invalid sort params #{@filter_params[:sort]}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end

    "#{field} #{order}"
  end

  def entity_joins
    if @look_up_join_entities.compact.present?
      @look_up_join_entities.uniq.map do |entity_type|
        "LEFT OUTER JOIN email_look_ups #{entity_type}_elu ON emails.id = #{entity_type}_elu.email_id AND #{entity_type}_elu.tenant_id = #{@auth_data.tenant_id} AND #{entity_type}_elu.deleted = FALSE LEFT OUTER JOIN look_ups #{entity_type}_lu ON #{entity_type}_elu.look_up_id = #{entity_type}_lu.id AND #{entity_type}_lu.entity_type = '#{entity_type}' AND #{entity_type}_lu.deleted = FALSE"
      end.join(' ')
    end
  end

  def paginate(sql)
    page = @filter_params[:page] || 1
    size = @filter_params[:size] || 10
    Email.includes(:email_track_logs, :email_link_logs, :email_threads).paginate_by_sql(sql, page: page.to_i, per_page: size.to_i)
  end

  def inner_join_sender
    if @inner_join_sender_relation
      "INNER JOIN look_ups sender_lookup ON emails.sender_id = sender_lookup.id AND sender_lookup.deleted = FALSE"
    end
  end

  def inner_join_connected_account
    if @inner_join_connected_account_relation
      'INNER JOIN connected_accounts ON emails.connected_account_id = connected_accounts.id'
    end
  end

  def left_outer_join_attachments
    if @left_outer_join_attachments_relation
      "LEFT OUTER JOIN (SELECT emails.email_thread_id, SUM(emails.external_attachment_count) AS email_thread_external_attachment_count FROM emails WHERE emails.deleted = FALSE AND emails.tenant_id = #{@auth_data.tenant_id} GROUP BY emails.email_thread_id) AS count_attachments ON emails.email_thread_id = count_attachments.email_thread_id"
    end
  end

  def left_outer_join_read_unread
    if @left_outer_join_read_unread_relation
      "LEFT OUTER JOIN (SELECT emails.email_thread_id, bool_and((CASE WHEN emails.read_by @> ARRAY[#{@auth_data.user_id}]::bigint[] THEN TRUE ELSE FALSE END)) AS read_unread_value FROM emails WHERE emails.tenant_id = #{@auth_data.tenant_id} AND emails.deleted = false GROUP BY emails.email_thread_id) AS read_unread_view ON read_unread_view.email_thread_id = emails.email_thread_id"
    end
  end

  def get_related_entity
    return unless @filter_params[:jsonRule]
    if rules = @filter_params[:jsonRule][:rules]
      related_rule = rules.find {|rule| "related_lookup" == rule[:type] }

      if related_rule && value = related_rule[:value]
        look_up = {id: value[:id],  type: value[:entity]}
      end
    end
    look_up
  end

  def conditions
    and_conditions_array = ["emails.tenant_id = #{@auth_data.tenant_id}"]

    entity = get_related_entity
    if entity.present?
      look_up_type = entity[:type]
      look_up_id = entity[:id]
      look_up_ids = look_up_id.is_a?(Array) ? look_up_id.join(',') : look_up_id
      and_conditions_array << "#{look_up_type}_ids && ARRAY[#{look_up_ids}]"
    end

    unless @auth_data.can_read_all_emails?
      or_conditions_array = [
        "emails.owner_id = #{@auth_data.user_id}",
        "email_threads.owner_id = #{@auth_data.user_id}",
        "all_lu.owner_id = #{@auth_data.user_id}",
        "all_lu.entity = '#{LOOKUP_USER}_#{@auth_data.user_id}'"
      ]

      @auth_data.load_shared_entities
      [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].each do |entity_type|
        owner_ids = @auth_data.shared_entities[entity_type][:shared_entity_owners].join(',')
        if owner_ids.present?
          or_conditions_array << "(all_lu.entity_type = '#{entity_type}' AND all_lu.owner_id IN (#{owner_ids}))"
        end
      end

      entities = @auth_data.shared_entities.values.map { |shared_hash| shared_hash[:shared_entity_formatted] }.flatten.compact.reject(&:blank?)
      if entities.present?
        or_conditions_array << "all_lu.entity IN ('#{entities.join("','")}')"
      end

      and_conditions_array << "(#{or_conditions_array.join(' OR ')})"
    end

    and_conditions_array += filters

    and_conditions_array.join(' AND ')
  end

  def filters
    return [] unless @filter_params[:jsonRule]

    @filter_params[:jsonRule] = @filter_params[:jsonRule].to_h.with_indifferent_access
    filters_conditions_array = []

    if rules = @filter_params[:jsonRule][:rules]
      validate(rules)

      filters_conditions_array += entity_fields_type_conditions(rules)
      filters_conditions_array += direction_field_conditions(rules)
      filters_conditions_array += connected_account_user_field_conditions(rules)
      filters_conditions_array += email_thread_condition(rules)
      filters_conditions_array += search_text_conditions(rules)
      filters_conditions_array += text_field_conditions(rules)
      filters_conditions_array += sent_by_received_by_conditions(rules)
      filters_conditions_array += date_field_conditions(rules)
      filters_conditions_array += associated_entity_conditions(rules)
      filters_conditions_array += attachment_conditions(rules)
      filters_conditions_array += read_unread_conditions(rules)
      filters_conditions_array += opened_at_conditions(rules)
      filters_conditions_array += clicked_at_conditions(rules)
      filters_conditions_array += email_status_conditions(rules)
    end

    filters_conditions_array
  end

  def entity_fields_type_conditions(rules)
    filtered_rules = rules.select { |rule| USER_FIELDS_RULE_IDS.include?(rule['field']) }

    query = []

    if filtered_rules.present?
      user_rule = filtered_rules.find { |rule| rule['primaryField'] == 'user' }
      sent_by_rule = filtered_rules.find { |rule| rule['primaryField'] == 'sentBy' }
      received_by_rule = filtered_rules.find { |rule| rule['primaryField'] == 'receivedBy' }

      if user_rule.present?
        user_rule_filtered_ids = FetchUserIdsByProperty.call(
          @auth_data.tenant_id,
          @auth_data.user_id,
          user_rule
        )

        @inner_join_connected_account_relation = true
        field = 'connected_accounts.user_id'

        query <<
        if user_rule_filtered_ids.success? && user_rule_filtered_ids.result.present?
          if user_rule_filtered_ids.result.length == 1
            "#{field} = #{user_rule_filtered_ids.result.first}"
          else
            "#{field} in (#{user_rule_filtered_ids.result.join(',')})"
          end
        else
          "#{field} is null"
        end
      end

      if sent_by_rule.present?
        sent_by_rule_filtered_ids = FetchUserIdsByProperty.call(
          @auth_data.tenant_id,
          @auth_data.user_id,
          sent_by_rule
        )

        field = 'sender_lookup.entity'
        @inner_join_sender_relation = true

        query <<
        if sent_by_rule_filtered_ids.success? && sent_by_rule_filtered_ids.result.present?
          if sent_by_rule_filtered_ids.result.length == 1
            "#{field} = '#{user_lookup_value(sent_by_rule_filtered_ids.result.first)}'"
          else
            "#{field} in ('#{sent_by_rule_filtered_ids.result.map { |v| user_lookup_value(v) }.join("','")}')"
          end
        else
          "#{field} is null"
        end
      end

      if received_by_rule.present?
        received_by_filtered_ids = FetchUserIdsByProperty.call(
          @auth_data.tenant_id,
          @auth_data.user_id,
          received_by_rule
        )

        additional_condition = "(user_elu.recipient_type = 0 OR user_elu.recipient_type = 1 OR user_elu.recipient_type = 2) AND "

        field = 'user_lu.entity'
        @look_up_join_entities << 'user'

        query <<
        if received_by_filtered_ids.success? && received_by_filtered_ids.result.present?
          if received_by_filtered_ids.result.length == 1
            "#{additional_condition} #{field} = '#{user_lookup_value(received_by_filtered_ids.result.first)}'"
          else
            "#{additional_condition} #{field} in ('#{received_by_filtered_ids.result.map { |v| user_lookup_value(v) }.join("','")}')"
          end
        else
          "#{additional_condition} #{field} is null"
        end
      end
    end

    query
  end

  def direction_field_conditions(rules)
    direction_field_rule = rules.find { |rule| rule[:field] == DIRECTION }
    direction_field_conditions = []

    if direction_field_rule.present?
      field = 'emails.direction'

      case direction_field_rule['operator']
      when 'is_null', 'is_not_null'
        direction_field_conditions << "#{field} #{OPERATOR_MAPPING[direction_field_rule['operator']]}"
      when 'equal', 'not_equal'
        direction_field_conditions << "#{field} #{OPERATOR_MAPPING[direction_field_rule['operator']]} #{Email.directions[direction_field_rule['value']]}"
      when 'in', 'not_in'
        direction_field_conditions << "#{field} #{OPERATOR_MAPPING[direction_field_rule['operator']]} (#{direction_field_rule['value'].split(',').flatten.map { |value| Email.directions[value.squish] }.compact.join(',')})"
      end
    end

    direction_field_conditions
  end

  def connected_account_user_field_conditions(rules)
    user_rule = rules.find { |rule| rule['field'] == 'user' }

    user_field_conditions = []

    if user_rule.present?
      @inner_join_connected_account_relation = true
      field = 'connected_accounts.user_id'

      case user_rule['operator']
      when 'is_null', 'is_not_null'
        user_field_conditions << "#{field} #{OPERATOR_MAPPING[user_rule['operator']]}"
      when 'equal', 'not_equal'
        user_field_conditions << "#{field} #{OPERATOR_MAPPING[user_rule['operator']]} #{user_rule['value']}"
      when 'in', 'not_in'
        user_field_conditions << "#{field} #{OPERATOR_MAPPING[user_rule['operator']]} (#{user_rule['value'].split(',').flatten.join(',')})"
      end
    end

    user_field_conditions
  end

  def email_thread_condition(rules)
    email_thread_rule = rules.find { |rule| (%w[email_thread_id id].include?(rule[:field])) }
    conditions = []

    if email_thread_rule.present?
      value = email_thread_rule[:value]
      case email_thread_rule['operator']
      when *%w[in not_in]
        conditions << "emails.email_thread_id #{OPERATOR_MAPPING[email_thread_rule['operator']]} (#{value.split(',').flatten.compact.reject(&:blank?).map(&:to_i).join(',')})"
      when *%w[is_null is_not_null]
        conditions << "emails.email_thread_id #{OPERATOR_MAPPING[email_thread_rule['operator']]}"
      when *%w[equal not_equal greater less greater_or_equal less_or_equal]
        conditions << "emails.email_thread_id #{OPERATOR_MAPPING[email_thread_rule['operator']]} #{value.to_i}"
      when *%w[between not_between]
        conditions << "(emails.email_thread_id #{OPERATOR_MAPPING[email_thread_rule['operator']]} #{value.first.to_i} and #{value.last.to_i})"
      end
    end

    conditions
  end

  def search_text_conditions(rules)
    multi_field_rule = rules.find { |rule| "multi_field" == rule[:field] }

    if multi_field_rule && value = multi_field_rule[:value]
      or_conditions_array = [
        "array_to_string(emails.to, ',') ILIKE '%%#{escaped(value)}%%'",
        "array_to_string(emails.cc, ',') ILIKE '%%#{escaped(value)}%%'",
        "array_to_string(emails.bcc, ',') ILIKE '%%#{escaped(value)}%%'",
        "emails.from ILIKE '%%#{escaped(value)}%%'",
        "emails.subject ILIKE '%%#{escaped(value)}%%'"
      ]
      ["(#{or_conditions_array.join(' OR ')})"]
    else
      []
    end
  end

  def text_field_conditions(rules)
    string_rules = rules.select { |rule| rule['type'] == 'string' && [DIRECTION, 'attachments', 'read', 'status'].exclude?(rule['field']) }

    string_rules.inject([]) do |arr, rule|
      field = rule['field']
      if field == 'subject'
        field = 'emails.subject'
      end

      case rule['operator']
      when *%w[is_null is_not_null is_empty is_not_empty]
        arr << "#{field} #{OPERATOR_MAPPING[rule['operator']]}"
      when 'equal'
        arr << "#{field} ILIKE '#{escaped(rule['value'])}'"
      when 'not_equal'
        arr << "#{field} NOT ILIKE '#{escaped(rule['value'])}'"
      when *%w[in not_in]
        arr << "lower(#{field}) #{OPERATOR_MAPPING[rule['operator']]} ('#{rule['value'].split(',').flatten.compact.reject(&:blank?).map(&:strip).map(&:downcase).join("','")}')"
      when 'begins_with'
        arr << "#{field} ILIKE '#{escaped(rule['value'])}%%'"
      when *%w[contains not_contains]
        arr << "#{field} #{OPERATOR_MAPPING[rule['operator']]} '%%#{escaped(rule['value'])}%%'"
      end

      arr
    end
  end

  def email_status_conditions(rules)
    status_rule = rules.find { |rule| rule['field'] == 'status' }
    return [] unless status_rule.present?

    case status_rule['operator']
    when 'equal', 'not_equal'
      status = Email.statuses[status_rule['value'].downcase]
      ["emails.status #{OPERATOR_MAPPING[status_rule['operator']]} #{status}"]
    when 'in', 'not_in'
      statuses = if status_rule['value'].is_a?(Array)
                   status_rule['value'].map { |val| Email.statuses[val.downcase] }
                 else
                   status_rule['value'].split(',').map { |val| Email.statuses[val.strip.downcase] }
                 end

      ["emails.status #{OPERATOR_MAPPING[status_rule['operator']]} (#{statuses.join(',')})"]
    when 'is_null', 'is_not_null'
      ["emails.status #{OPERATOR_MAPPING[status_rule['operator']]}"]
    end
  end

  def sent_by_received_by_conditions(rules)
    long_rules = rules.select { |rule| %w[sentBy receivedBy].include?(rule['field']) }

    long_rules.inject([]) do |arr, rule|
      field = rule['field'].underscore
      if field == 'sent_by'
        field = 'sender_lookup.entity'
        @inner_join_sender_relation = true
      elsif field == 'received_by'
        # 0: to, 1: cc, 2: bcc
        additional_condition = "(user_elu.recipient_type = 0 OR user_elu.recipient_type = 1 OR user_elu.recipient_type = 2) AND "
        field = 'user_lu.entity'
        @look_up_join_entities << 'user'
      end

      case rule['operator']
      when *%w[is_null is_not_null]
        arr << "#{additional_condition} #{field} #{OPERATOR_MAPPING[rule['operator']]}"
      when *%w[equal not_equal]
        arr << "#{additional_condition} #{field} #{OPERATOR_MAPPING[rule['operator']]} '#{user_lookup_value(rule['value'])}'"
      when *%w[in not_in]
        arr << "#{additional_condition} #{field} #{OPERATOR_MAPPING[rule['operator']]} ('#{rule['value'].split(',').flatten.map { |v| user_lookup_value(v) }.join("','")}')"
      end

      arr
    end
  end

  def date_field_conditions(rules)
    date_rules = rules.select { |rule| rule['type'] == 'date' && ['openedAt', 'clickedAt'].exclude?(rule['field']) }

    date_rules.inject([]) do |arr, rule|
      field = rule['field'].underscore
      if field == 'created_at'
        field = 'emails.created_at'
      end

      case rule['operator']
      when *%w[is_null is_not_null]
        arr << "#{field} #{OPERATOR_MAPPING[rule['operator']]}"
      when *%w[equal not_equal greater less greater_or_equal less_or_equal]
        arr << "#{field} #{OPERATOR_MAPPING[rule['operator']]} '#{extract_datetime(rule['value'])}'"
      when *%w[between not_between]
        arr << "(#{field} #{OPERATOR_MAPPING[rule['operator']]} '#{extract_datetime(rule['value'].first)}' and '#{extract_datetime(rule['value'].last)}')"
      when *RELATIVE_FILTERS_LIST
        arr << RelativeFilters.new(field, rule['timeZone'], rule['value']).public_send(rule['operator'])
      end

      arr
    end
  end

  def associated_entity_conditions(rules)
    associated_entity_rules = rules.select { |rule| %w[associatedLeads associatedDeals associatedContacts].include?(rule['field']) }
    arr = []
    Rails.logger.info "associated_entity_conditions: #{rules}"
    associated_entity_rules.each do |rule|
      entity_type = rule['field'].underscore.gsub('associated_', '').singularize
      case rule['operator']
      when 'is_null'
        arr << "email_threads.#{entity_type}_ids = '{}'"
      when 'is_not_null'
        arr << "email_threads.#{entity_type}_ids <> '{}'"
      else
        arr << "email_threads.#{entity_type}_ids && ARRAY[#{rule['value']}]" unless rule['value'].blank?
      end
    end
    Rails.logger.info "arr: #{arr}"
    arr
  end

  def attachment_conditions(rules)
    attachment_rule = rules.find { |rule| rule['field'] == 'attachments' }
    attachment_conditions_array = []

    if attachment_rule.present?
      case attachment_rule['operator']
      when 'is_null'
        @left_outer_join_attachments_relation = true
        attachment_conditions_array << "count_attachments.email_thread_external_attachment_count = 0"
      else
        attachment_conditions_array << "emails.external_attachment_count > 0"
      end
    end

    attachment_conditions_array
  end

  def read_unread_conditions(rules)
    read_unread_rule = rules.find { |rule| rule['field'] == 'read' }
    read_unread_conditions_array = []

    if read_unread_rule.present?
      case read_unread_rule['operator']
      when 'equal'
        case read_unread_rule['value']
        when 'true', true
          @left_outer_join_read_unread_relation = true
          read_unread_conditions_array << "read_unread_view.read_unread_value = #{read_unread_rule['value']}"
        when 'false', false
          read_unread_conditions_array << "NOT(emails.read_by @> ARRAY[#{@auth_data.user_id}]::bigint[])"
        end
      when 'not_equal'
        case read_unread_rule['value']
        when 'true', true
          read_unread_conditions_array << "NOT(emails.read_by @> ARRAY[#{@auth_data.user_id}]::bigint[])"
        when 'false', false
          @left_outer_join_read_unread_relation = true
          read_unread_conditions_array << "read_unread_view.read_unread_value != #{read_unread_rule['value']}"
        end
      end
    end

    read_unread_conditions_array
  end

  def opened_at_conditions(rules)
    opened_at_rule = rules.find { |rule| rule['field'] == 'openedAt' }
    opened_at_conditions_array = []

    if opened_at_rule.present?
      field = "email_track_logs.created_at"
      case opened_at_rule['operator']
      when *%w[is_null is_not_null]
        email_tracking_filter_condition = "#{field} is not null"
      when *%w[equal not_equal greater less greater_or_equal less_or_equal]
        email_tracking_filter_condition = "#{field} #{OPERATOR_MAPPING[opened_at_rule['operator']]} '#{extract_datetime(opened_at_rule['value'])}'"
      when *%w[between not_between]
        email_tracking_filter_condition = "(#{field} #{OPERATOR_MAPPING[opened_at_rule['operator']]} '#{extract_datetime(opened_at_rule['value'].first)}' and '#{extract_datetime(opened_at_rule['value'].last)}')"
      when *RELATIVE_FILTERS_LIST
        email_tracking_filter_condition = RelativeFilters.new(field, opened_at_rule['timeZone'], opened_at_rule['value']).public_send(opened_at_rule['operator'])
      end

      @left_outer_join_opened_at_relation = "LEFT OUTER JOIN (SELECT email_track_logs.email_id, bool_or(email_track_logs.created_at IS NOT NULL) AS opened_at_value FROM email_track_logs WHERE email_track_logs.tenant_id = #{@auth_data.tenant_id} AND #{email_tracking_filter_condition} GROUP BY email_track_logs.email_id) AS opened_at_view ON opened_at_view.email_id = emails.id"

      if opened_at_rule['operator'] == 'is_null'
        opened_at_conditions_array << "opened_at_view.opened_at_value IS NULL"
      else
        opened_at_conditions_array << "opened_at_view.opened_at_value = true"
      end
    end

    opened_at_conditions_array
  end

  def clicked_at_conditions(rules)
    clicked_at_rule = rules.find { |rule| rule['field'] == 'clickedAt' }
    clicked_at_conditions_array = []

    if clicked_at_rule.present?
      field = "email_link_logs.created_at"
      case clicked_at_rule['operator']
      when *%w[is_null is_not_null]
        email_tracking_filter_condition = "#{field} is not null"
      when *%w[equal not_equal greater less greater_or_equal less_or_equal]
        email_tracking_filter_condition = "#{field} #{OPERATOR_MAPPING[clicked_at_rule['operator']]} '#{extract_datetime(clicked_at_rule['value'])}'"
      when *%w[between not_between]
        email_tracking_filter_condition = "(#{field} #{OPERATOR_MAPPING[clicked_at_rule['operator']]} '#{extract_datetime(clicked_at_rule['value'].first)}' and '#{extract_datetime(clicked_at_rule['value'].last)}')"
      when *RELATIVE_FILTERS_LIST
        email_tracking_filter_condition = RelativeFilters.new(field, clicked_at_rule['timeZone'], clicked_at_rule['value']).public_send(clicked_at_rule['operator'])
      end

      @left_outer_join_clicked_at_relation = "LEFT OUTER JOIN (SELECT email_link_logs.email_id, bool_or(email_link_logs.created_at IS NOT NULL) AS clicked_at_value FROM email_link_logs WHERE email_link_logs.tenant_id = #{@auth_data.tenant_id} AND #{email_tracking_filter_condition} GROUP BY email_link_logs.email_id) AS clicked_at_view ON clicked_at_view.email_id = emails.id"

      if clicked_at_rule['operator'] == 'is_null'
        clicked_at_conditions_array << "clicked_at_view.clicked_at_value IS NULL"
      else
        clicked_at_conditions_array << "clicked_at_view.clicked_at_value = true"
      end
    end

    clicked_at_conditions_array
  end

  def user_lookup_value(user_id)
    "#{LOOKUP_USER}_#{user_id}"
  end

  def entity_lookup_value(entity_type, entity_id)
    "#{entity_type}_#{entity_id}"
  end

  def validate(rules)
    if rules.any? { |rule| invalid_rule_type_and_operator?(rule) || invalid_field?(rule) }
      Rails.logger.error "FilterEmailsQuery Invalid rules #{rules.inspect}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end
  end

  def invalid_rule_type_and_operator?(rule)
    (
      !@field_wise_filters[rule['type']] ||
      @field_wise_filters[rule['type']].exclude?(rule['operator']) ||
      (%w[between not_between].include?(rule['operator']) && !rule['value'].is_a?(Array)) ||
      (%w[last_n_days next_n_days].include?(rule['operator']) && (!rule['value'].is_a?(Integer) || rule['value'].to_i <= 0 || rule['value'].to_i > 364)) ||
      field_specific_validation(rule)
    )
  end

  def invalid_field?(rule)
    @filterable_fields.exclude?(rule['field']) || (rule['field'] == 'receivedBy' && %w[is_null is_not_null].include?(rule['operator']))
  end

  def escaped(value)
    value.gsub('_') { "\\_" }
      .gsub('%') { "\\%" }
  end

  def field_specific_validation(rule)
    case rule['field']
    when DIRECTION
      (
        %w[equal not_equal in not_in is_null is_not_null].exclude?(rule['operator']) ||
        (
          rule['value'].present? &&
          rule['value'].split(',').flatten.any? { |val| Email.directions.keys.exclude?(val.strip) }
        )
      )
    when 'attachments'
      %w[is_null is_not_null].exclude?(rule['operator'])
    when 'read'
      [true, 'true', false, 'false'].exclude?(rule['value'])
    end
  end

  def extract_datetime(value)
    return nil if value.blank?

    value = value.to_s
    begin
      unless value.include?('-')
        value = Time.at(value.to_i / 1000.to_f).utc.strftime('%FT%T.%LZ')
      end
    rescue => e
      Rails.logger.error "FilterEmailsQuery Unable to parse date #{value.inspect} message #{e.message}"
      raise(ExceptionHandler::InvalidDataError, ErrorCode.invalid)
    end

    value
  end
end
