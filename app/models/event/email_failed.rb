# frozen_string_literal: true

class Event::EmailFailed
  def initialize(email, old_serialized_email_data)
    @email = email
    @old_serialized_email_data = old_serialized_email_data
  end

  def routing_key
    EMAIL_FAILED
  end

  def as_json(options = {})
    super
  end

  def to_json(options = {})
    {
      entity: EmailDetailsSerializer.call(@email, false, nil, false, add_owner_id: true, extra_fields: %w[failed_at]).result,
      oldEntity: @old_serialized_email_data,
      metadata: {
        tenantId: @email.tenant_id,
        userId: @email.owner.id,
        entityType: "EMAIL",
        entityId: @email.id,
        entityAction: "EMAIL_FAILED",
        executedWorkflows: @email.metadata&.dig('executedWorkflows') || []
      }
    }.to_json
  end
end
