# frozen_string_literal: true

class GmailBounceDetector
  # Gmail-specific bounce subject patterns
  # BOUNCE_SUBJECTS = [
  #   /delivery.*status.*notification.*failure/i,
  #   /delivery.*status.*notification.*delay/i,
  #   /delivery.*status.*notification/i,
  #   /undelivered.*mail.*returned.*sender/i,
  #   /undeliverable/i,
  #   /mail.*delivery.*failed/i,
  #   /returned.*mail/i,
  #   /delivery.*failure/i,
  #   /message.*not.*delivered/i,
  #   /address.*not.*found/i,
  #   /mail.*system.*error/i,
  #   /postmaster.*notification/i
  # ].freeze

  # Hard bounce indicators (permanent failures)
  HARD_BOUNCE_PATTERNS = [
    /5\.\d+\.\d+/,  # 5.x.x SMTP codes
    /550/,          # Permanent failure
    /551/,          # User not local
    /552/,          # Exceeded storage allocation
    /553/,          # Mailbox name not allowed
    /554/,          # Transaction failed
    /account.*does.*not.*exist/i,
    /recipient.*address.*rejected/i,
    /user.*unknown/i,
    /no.*such.*user/i,
    /invalid.*recipient/i,
    /permanent.*failure/i
  ].freeze

  # Soft bounce indicators (temporary failures)
  SOFT_BOUNCE_PATTERNS = [
    /4\.\d+\.\d+/,  # 4.x.x SMTP codes
    /421/,          # Service not available
    /422/,          # Recipient mailbox full
    /450/,          # Requested action not taken
    /451/,          # Requested action aborted
    /452/,          # Insufficient system storage
    /mailbox.*full/i,
    /quota.*exceeded/i,
    /temporarily.*unavailable/i,
    /try.*again.*later/i,
    /deferred/i,
    /temporary.*failure/i
  ].freeze

  # Gmail-specific bounce sender patterns
  BOUNCE_SENDERS = [
    /mailer-daemon/i,
    /mail.*delivery.*subsystem/i,
    /postmaster/i,
    /noreply.*daemon/i,
    /delivery.*status/i
  ].freeze

  def initialize(message_data)
    @message = message_data
    @headers = extract_headers
    #@subject = extract_subject
    @body = extract_body
    @from = extract_from
  end

  def self.call(message_data)
    new(message_data).detect_bounce
  end

  def detect_bounce
    Rails.logger.info "EMAIL_BOUNCE: GmailBounceDetector: Starting Bounce detection"
    Rails.logger.info "EMAIL_BOUNCE: GmailBounceDetector: bounce_by_sender? #{bounce_by_sender?} bounce_by_content? #{bounce_by_content?}"

    return { is_bounced: false } unless is_bounced?

    {
      is_bounced: true,
      bounce_type: determine_bounce_type,
      failed_reason: extract_failed_reason,
      original_message_id: extract_original_message_id
    }
  end

  private

  def is_bounced?
    bounce_by_sender? && bounce_by_content?
  end

  # def bounce_by_subject?
  #   return false unless @subject

  #   Rails.logger.info "Subject is #{@subject}"
  #   BOUNCE_SUBJECTS.any? { |pattern| @subject.match?(pattern) }
  # end

  def bounce_by_sender?
    return false unless @from

    Rails.logger.info "EMAIL_BOUNCE: GmailBounceDetector: From is #{@from}"
    BOUNCE_SENDERS.any? { |pattern| @from.match?(pattern) }
  end

  def bounce_by_content?
    return false unless @body

    content = @body.downcase
    (HARD_BOUNCE_PATTERNS + SOFT_BOUNCE_PATTERNS).any? { |pattern| content.match?(pattern) }
  end

  def determine_bounce_type
    content = @body.downcase

    return 'hard' if HARD_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    
    return 'soft' if SOFT_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    
    'hard'
  end

  def extract_failed_reason
    content = @body.to_s

    patterns = [
      /Diagnostic-Code:\s*.+?;\s*(.+)/i,
      /Technical details of permanent failure:\s*(.+)/i,
      /The error that the other server returned was:\s*(.+)/i,

      /(5\.\d+\.\d+.*)/i,
      /(4\.\d+\.\d+.*)/i,

      /ERROR CODE:\s*(.+)/i,
      /SMTP.*ERROR:\s*(.+)/i
    ]

    patterns.each do |pattern|
      match = content.match(pattern)
      return match[1].strip if match && match[1]
    end

    content.each_line.map(&:strip).reject(&:empty?).first
  end

  def extract_headers
    @message.payload&.headers || []
  end

  # def extract_subject
  #   subject_header = @headers.find { |h| h.name&.downcase == 'subject' }
  #   Rails.logger.info "Bounce Detection: Extracted subject from @message #{subject_header}"
  #   subject_header&.value
  # end

  def extract_from
    from_header = @headers.find { |h| h.name&.downcase == 'from' }
    from_header&.value
  end

  def extract_body
    # Extract body from Gmail message payload
    if @message.payload&.body&.data
      Base64.decode64(@message.payload.body.data)
    elsif @message.payload&.parts.present?
      # Handle multipart messages
      text_part = find_delivery_status_text_part(@message.payload.parts)
      text_part || ''
    else
      ''
    end
  end

  def find_delivery_status_text_part(parts)
    delivery_status = parts&.find { |part| part.mime_type == 'message/delivery-status' }
    delivery_status = delivery_status&.parts&.find { |part| part.mime_type == 'text/plain' }
    delivery_status&.body&.data
  end

  def extract_original_message_id
    parts = @message.payload.parts
    delivery_status_headers = parts&.find { |part| part.mime_type == 'message/delivery-status' }
    delivery_status_headers = delivery_status_headers&.parts&.find { |part| part.mime_type == 'text/plain' }
    delivery_status_headers = delivery_status_headers&.headers&.find { |h| h.name&.downcase == 'x-original-message-id' }

    unless delivery_status_headers
      delivery_status_headers = @headers.find { |h| h.name&.downcase == 'in-reply-to' }
    end

    delivery_status_headers&.value
  end
end
