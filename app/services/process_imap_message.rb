class ProcessImapMessage
  prepend SimpleCommand
  include MessageProcessor

  def initialize(data)
    @data = data
    @provider_id = @data["data"]["emailId"] || @data["data"]["messageId"]
    @attachments = []
  end

  def call
    load_connected_account
    Rails.logger.info "#{@data['account']} Processing message process_imap_message"

    if @connected_account.blank?
      Rails.logger.info "#{@data['account']} ConnectedAccount not found" 
      return
    end

    if Email.where(connected_account_id: @connected_account.id, source_id: @data["data"]["id"], tenant_id: @connected_account.tenant_id).exists?
      Rails.logger.info "#{@data['account']} Email already exists by source_id #{@data["data"]["id"]}"
      return
    end

    if Email.where(connected_account_id: @connected_account.id, provider_id: @provider_id, tenant_id: @connected_account.tenant_id).exists?
      Rails.logger.info "#{@data['account']} Email already exists by provider_id #{@provider_id}"
    end
    
    if map_if_sent
      Rails.logger.info "#{@data['account']} Sent email updated by source id #{@data["data"]["messageId"]}"
      return
    end

    save_message
  end

  private

  def map_if_sent
    Rails.logger.info "#{@data['account']} Searching email #{@data["data"]["messageId"]}"
    email = Email.where(connected_account_id: @connected_account.id, tenant_id: @connected_account.tenant_id, source_id: @data["data"]["messageId"]).first
    return false if email.blank?

    if @data['event'].eql?('messageDeliveryError')
      email.update(status: Email.statuses['failed'])
    elsif @data['event'].eql?('messageFailed')
      email.update(status: Email.statuses['failed'], failed_reason: @data["data"]["error"])
    else
      email.update(source_id: @data["data"]["id"], provider_id: @provider_id, status: Email.statuses['sent'])
      email.email_thread.update(source_thread_id: @data["data"]["threadId"])
      Rails.logger.info "#{@data['account']} Email updated #{@data["data"]["messageId"]} source_id @#{@data["data"]["id"]} provider_id @#{@provider_id}"
      Rails.logger.info "#{@data['account']} updating thread #{email.email_thread_id} source_thread_id #{@data["data"]["threadId"]}"
    end
    return true
  end

  def load_connected_account
    @connected_account = ConnectedAccount.where(active: true, access_token: @data["account"]).first
  end

  def save_message
    return unless @data

    if Email.where(connected_account_id: @connected_account.id, tenant_id: @connected_account.tenant_id, source_id: @data["data"]["id"]).exists?
      Rails.logger.info "#{@data['account']} Email already exists while saving by source_id #{@data["data"]["id"]}"
      return
    end
    
    data = @data["data"]
    email = Email.new
    email.from = data["from"]["address"]
    email.to =  data["to"].map{|to| to["address"]} if data["to"]
    email.cc = data["cc"].map{|cc| cc["address"]} if data["cc"]
    email.bcc = data["bcc"].map{|bcc| bcc["address"]} if data["bcc"]
    email.provider_id = @data["data"]["emailId"]
    emails = (email.cc.to_a + email.bcc.to_a + email.to.to_a + [email.from])
    entities = associated_entities(emails, data, from_email_address: email.from)
    email.status = Email.statuses['sent'] if @connected_account.email == email.from

    if((entities[:email_thread].present?) || (entities[:related_look_ups].present? && entities[:related_look_ups][:matched].present?))
      from_lookup = entities[:related_look_ups][:matched].find{|l| l[:email] == email.from}
      #return unless from_lookup || entities[:email_thread].present?

      targeted_entities = entities[:related_look_ups][:matched].find{|l| [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].include? l[:entity]&.split('_')&.first}
      return unless targeted_entities || entities[:email_thread].present?

      unless from_lookup
        from_lookup = entities[:related_look_ups][:unmatched].find{|l| l[:email] == email.from}
      end
      email.subject = data["subject"]
      email.global_message_id = data["messageId"]
      email.source_id = data["id"]
      email.owner = @connected_account.user
      email.body = data["text"]["html"] || data["text"]["plain"]
      email.connected_account = @connected_account
      lookup = LookUp.find_or_initialize_by(from_lookup)
      lookup.save!
      email.sender = lookup
      email.tenant_id = @connected_account.user.tenant_id
      email_thread = entities[:email_thread] || create_email_thread
      email.email_thread_id = email_thread.id
      if Email.where(source_id: data["id"], connected_account_id: @connected_account.id).exists?
        Rails.logger.info "Email save log: Duplicate message with id: #{data["id"]} "
      else
        parse_attachments
        email.external_attachment_count = @attachments.present? ? @attachments.reject { |a| a[:type] == 'inline' }.count : 0
        email.save!
        email.related_to << lookup
        save_related_entities(email, entities, email_thread.emails.first&.related_to)
        SaveAttachmentsFromImap.call({account_id: @connected_account.access_token, email: email, attachments: @attachments})
        already_triggered = Email.where(tenant_id: email.tenant_id, global_message_id: email.global_message_id, is_trigerred: true).exists?
        unless already_triggered && email.global_message_id.present?
          EmailReceivedRelatedToEntityPublisher.call(email, false)
          email.is_trigerred = true
          email.save!
        end
        email_thread=  email.reload.email_thread
        contact_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_CONTACT).pluck(:entity_id) + email_thread.contact_ids.to_a).uniq
        lead_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_LEAD).pluck(:entity_id) + email_thread.lead_ids.to_a).uniq
        user_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_USER).pluck(:entity_id) + email_thread.user_ids.to_a).uniq
        deal_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_DEAL).pluck(:entity_id) + email_thread.deal_ids.to_a).uniq
        email_thread.update(lead_ids: lead_ids, user_ids: user_ids, deal_ids: deal_ids, contact_ids: contact_ids)

        EmailCreateEventPublisher.call(email.id, CREATE_ACTION)
        EmailCreatedWorkflowV2EventPublisher.call(email)
      end
    end
  end

  def create_email_thread
    Rails.logger.info "#{@data['account']} Creating new thread for #{@data["data"]["threadId"]}"
    EmailThread.create(tenant_id: @connected_account.tenant_id, owner_id: @connected_account.user_id, source_thread_id: @data["data"]["threadId"])
  end

  def get_email_thread message
    if message["threadId"].blank?
      if message['inReplyTo'].blank?
        Rails.logger.info "#{@data['account']} inReplyTo blank for message #{message['messageId']}"
        return nil
      end
      Rails.logger.info "#{@data['account']} searching thread for inReplyTo #{message['inReplyTo']}"
      thread = Email.where(global_message_id: message['inReplyTo'], tenant_id: @connected_account.tenant_id, connected_account_id: @connected_account.id, owner_id: @connected_account.user_id).last&.email_thread
      Rails.logger.info "#{@data['account']} #{message['inReplyTo']} Existing thread not Found" unless thread.present?
      Rails.logger.info "#{@data['account']} #{message['inReplyTo']} Existing thread Found #{thread.id}" if thread.present?
    else
      thread = EmailThread.where(source_thread_id: message["threadId"], tenant_id: @connected_account.tenant_id, owner_id: @connected_account.user_id).last
      Rails.logger.info "#{@data['account']} searched for existing thread for threadId #{message['threadId']} Found #{thread&.id}"
    end
    thread
  end

  def save_related_to email
    return if EmailLookUp.where(email_id: email.id).count.eql? 0

    email.related_to << email.email_thread.emails.order(:created_at).first.related_to if email.email_thread.emails.count > 0
  end

  def parse_attachments
    attachments = @data.dig("data", "attachments")
    return unless attachments
    attachments.each do |attachment|
      type = attachment["inline"] ? 'inline' : 'attachment'
      @attachments << {
        id: attachment["id"],
        name: attachment['filename'],
        type: type,
        content_id: attachment['contentId']
      }
    end
  end
end
