class SMTPBounceProcessor
  # Hard bounce indicators (permanent failures)
  HARD_BOUNCE_PATTERNS = [
    /5\.\d+\.\d+/,  # 5.x.x SMTP codes
    /550/,          # Permanent failure
    /551/,          # User not local
    /553/,          # Mailbox name not allowed
    /554/,          # Transaction failed
    /delivery has failed/i,
    /the following recipients? cannot be reached/i,
    /no such user/i,
    /mailbox (unavailable|not found)/i,
    /address rejected/i,
    /unrecognized.*recipient/i,
    /recipient.*address.*rejected/i,
    /user unknown/i,
    /no such user/i,
    /invalid recipient/i,
    /does not exist/i
  ].freeze

  # Soft bounce indicators (temporary failures)
  SOFT_BOUNCE_PATTERNS = [
    /4\.\d+\.\d+/,  # 4.x.x SMTP codes
    /421/,          # Service not available
    /422/,          # Recipient mailbox full
    /450/,          # Requested action not taken
    /451/,          # Requested action aborted
    /452/,          # Insufficient system storage
    /552/,          # Exceeded storage allocation
    /mailbox.*full/i,
    /quota.*exceeded/i,
    /temporarily.*unavailable/i,
    /try.*again.*later/i,
    /deferred/i,
    /temporary.*failure/i,
    /message.*delayed/i,
    /delivery.*delayed/i
  ].freeze

 def initialize(message_data)
    @message = message_data
  end

  def self.call(message_data)
    new(message_data).extract_bounce_info
  end

  def extract_bounce_info
    {
      is_bounced: true,
      bounce_type: determine_bounce_type,
      failed_reason: extract_failed_reason,
      original_message_id: extract_original_message_id
    }
  end

  private

  def determine_bounce_type
    content = @message.dig('data', 'response', 'message') || ''
    content += @message.dig('data', 'response', 'status') || ''

    if content
      return 'hard' if HARD_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
      return 'soft' if SOFT_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    end

    'hard'
  end

  def extract_failed_reason
    return @message.dig('data', 'response', 'message') || @message.dig('data', 'response', 'status')
  end

  def extract_original_message_id
    return @message.dig('data', 'messageId')
  end
end
