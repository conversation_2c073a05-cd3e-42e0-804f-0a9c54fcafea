class FetchOutlookMessages
  prepend SimpleCommand
  include GraphApiClient
  include MessageProcessor

  def initialize(params)
    @params = params
    @connected_account = nil
  end

  def call
    @params.each do |data|
      data = data.with_indifferent_access
      subscription_id = data[:subscriptionId]
      next unless subscription_id
      # get connected account using subscription id, get access token for fetching actual message details
      if @token = get_access_token(subscription_id)
        resource_details = data[:resourceData]
        message_id = resource_details[:id]
        next if Email.where(source_id: message_id).exists?

        begin
          message_details = get_message(message_id)
          save_message(message_details)
        rescue => e
          Rails.logger.error "Get outlook message error for message id::#{message_id}:: #{e.message}"
          next
        end
      end
    end
  end

  private

  def get_access_token(subscription_id)
    @connected_account = ConnectedAccount.find_by_subscription_id(subscription_id)
    return unless @connected_account
    return unless @connected_account.active
    add_token_in_current_thread
    command = GetConnectedAccountAccessToken.call(@connected_account, skip_auth: true)
    command.result if command.success?
  end

  def save_message message
    return unless message

    message = message.with_indifferent_access
    # Don't save if message is draft
    if message[:isDraft]
      return
    end
    return if Email.where(source_id: message[:id]).exists?

    skip_event_publishing = false

    begin
      bounce_info = BounceProcessor.call(message, @connected_account, { token: @token }).result
      skip_event_publishing = true if bounce_info[:is_bounced]
    rescue => e
      Rails.logger.info "Bounce Detection Error #{e.message}\n#{e.backtrace.join("\n")}"
    end

    email = Email.new
    from_email = get_email_ids([message[:from]]).first
    email.from = from_email
    email.to = get_email_ids(message[:toRecipients])
    email.cc = get_email_ids(message[:ccRecipients])
    email.bcc = get_email_ids(message[:bccRecipients])
    email.status = Email.statuses['sent'] if @connected_account.email == email.from

    emails = (email.cc.to_a + email.bcc.to_a + email.to.to_a + [email.from])
    entities = associated_entities(emails, message, from_email_address: from_email)

    if((entities[:email_thread].present?) || (entities[:related_look_ups].present? && entities[:related_look_ups][:matched].present?))
      from_lookup = entities[:related_look_ups][:matched].find{|l| l[:email] == email.from}
      #return unless from_lookup || entities[:email_thread].present?

      targeted_entities = entities[:related_look_ups][:matched].find{|l| [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT].include? l[:entity]&.split('_')&.first}
      return unless targeted_entities || entities[:email_thread].present?

      unless from_lookup
        from_lookup = entities[:related_look_ups][:unmatched].find{|l| l[:email] == email.from}
      end
      email.subject = message[:subject]
      email.global_message_id = message[:internetMessageId]
      email.source_id = message[:id]
      email.owner = @connected_account.user
      email.body = message[:body][:content]
      email.connected_account = @connected_account
      lookup = LookUp.find_or_initialize_by(from_lookup)
      lookup.save!
      email.sender = lookup
      email.tenant_id = @connected_account.user.tenant_id
      email_thread = entities[:email_thread] || create_email_thread(message)
      email.email_thread_id = email_thread.id
      if Email.where(source_id: message[:id]).exists?
        Rails.logger.info "Outlook:: Email save log: Duplicate message with id: #{message[:id]}"
      else
        email.save!
        email.related_to << lookup
        save_related_entities(email, entities, email_thread.emails.first&.related_to)
        save_attachements(email, message)
        email.update_column(:external_attachment_count, Attachment.where(email_id: email.id, inline: false).count)
        already_triggered = Email.where(tenant_id: email.tenant_id, global_message_id: email.global_message_id, is_trigerred: true).exists?
        unless already_triggered && email.global_message_id.present?
          EmailReceivedRelatedToEntityPublisher.call(email, false) unless skip_event_publishing
          email.is_trigerred = true
          email.save!
        end
        email_thread=  email.reload.email_thread
        contact_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_CONTACT).pluck(:entity_id) + email_thread.contact_ids.to_a).uniq
        lead_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_LEAD).pluck(:entity_id) + email_thread.lead_ids.to_a).uniq
        user_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_USER).pluck(:entity_id) + email_thread.user_ids.to_a).uniq
        deal_ids = (email.email_look_ups.joins(:look_up).where("look_ups.entity_type": LOOKUP_DEAL).pluck(:entity_id) + email_thread.deal_ids.to_a).uniq
        email_thread.update(lead_ids: lead_ids, user_ids: user_ids, deal_ids: deal_ids, contact_ids: contact_ids)

        Rails.logger.info "Skipping outlook email create event publishing for bounced email #{email.id} tenant id #{email.tenant_id}" if skip_event_publishing

        EmailCreateEventPublisher.call(email.id, CREATE_ACTION) unless skip_event_publishing
        EmailCreatedWorkflowV2EventPublisher.call(email) unless skip_event_publishing
      end
    end
  end

  def save_attachements(email, message)
    data = fetch_attachments_for_message(message[:id])
    attachments_to_create = []
    data.each do |attachment|
      if attachment['@odata.type'] == '#microsoft.graph.fileAttachment'
        attachment_file = File.new("#{Rails.root}/tmp/#{DateTime.now.to_i}_#{@connected_account.id}_#{attachment['name']}", 'wb')

        attachment_body = Base64.decode64(attachment['contentBytes'])
        attachment_file.write(attachment_body)

        disposition_type = attachment['isInline'] ? 'inline' : 'attachment'

        attachments_to_create <<  HashWithIndifferentAccess.new({
          data: attachment_file,
          fileName: attachment['name'],
          type: disposition_type,
          content_id: attachment['contentId']
        })
      end
    end
    CreateAttachment.call(email, attachments_to_create, nil, true) if attachments_to_create.present?
  end

  def get_email_ids(emails)
    recipients = []
    emails.each do |data|
      recipients << data[:emailAddress][:address]
    end
    recipients
  end

  def create_email_thread message
    EmailThread.create(tenant_id: @connected_account.tenant_id, owner_id: @connected_account.user_id, source_thread_id: message[:conversationId])
  end

  def get_email_thread message
    thread = EmailThread.where(source_thread_id: message[:conversationId]).last
    thread
  end

  def save_related_to email
    return unless EmailLookUp.where(email_id: email.id).exists?

    email.related_to << email.email_thread.emails.order(:created_at).first.related_to if email.email_thread.emails.count > 1
  end
end
