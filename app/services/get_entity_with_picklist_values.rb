require 'rest-client'

DATE_FORMAT_MAPPING = { "MMM D, YYYY [at] h:mm a" => "%b %d,%Y", "DD-MM-YYYY HH:mm:ss" => '%e-%m-%Y', "MM/DD/YYYY HH:mm:ss" => "%m/%d/%Y"}
DATE_TIME_FORMAT_MAPPING = { "MMM D, YYYY [at] h:mm a" => "%b %d,%Y at%l:%M %P", "DD-MM-YYYY HH:mm:ss" => '%e-%m-%Y %k:%M:%S', "MM/DD/YYYY HH:mm:ss" => "%m/%d/%Y %k:%M:%S"}

class GetEntityWithPicklistValues
  prepend SimpleCommand

  def initialize(entity_id, entity_type, child_entity_type = nil)
    @entity_id = entity_id
    @entity_type = entity_type
    @child_entity_type = child_entity_type
    @user_settings = UserSettingsService.new.fetch unless @skip_fetch_user_settings
    @timezone_abbreviations =  YAML.load_file(Rails.root.join('config/timezone_abbreviations.yml'))
  end

  def call
    command = GetSecurityContext.call("token")
    raise(ExceptionHandler::AuthenticationError, ErrorCode.unauthorized) unless command.success?

    if [TENANT, LOOKUP_USER].include?(@child_entity_type)
      @entity_fields = []
    else
      @entity_fields = V2::GetVariables.call(@entity_type).result
    end

    command = "Get#{current_entity}".constantize.call(@entity_id)
    return unless command.success?
    process_variables(command.result)
  end

  private

  def set_picklist_values(data)
    command = GetStandardPicklist.call
    return unless command.success?
    result = command.result

    picklist_hash = {
      'country' => 'COUNTRY',
      'companyCountry' => 'COUNTRY',
      'requirementCurrency' => 'CURRENCY',
      'companyIndustry' => 'INDUSTRY',
      'companyBusinessType' => 'BUSINESS_TYPE',
      'industry' => 'INDUSTRY',
      'language' => 'LANG',
      'currency' => 'CURRENCY',
      'timezone' => 'TIMEZONE'
    }

    picklist_hash.each do |field, type|
      data[field] = result[type].select{|hash| hash["name"].eql?(data[field])}.first&.[]("displayName") if data[field].present?
    end

    # This is only for Money fields on Deal
    @entity_fields.select { |field| field['type'] == 'MONEY' }.each do |field|
      if data[field['internalName']].present? && data[field['internalName']]['value'].present?
        currency_name = result["CURRENCY"].select{|cur| cur["id"].eql? (data[field['internalName']]['currencyId'])}.first&.[]("name")
        data[field['internalName']] = "#{currency_name} #{data[field['internalName']]['value']}"
      end
    end

    data
  end

  def parse_date_time(str)
    return nil if str.blank?
    str = str.to_s
    date = ''
    begin
      if str.include?('-')
        date = DateTime.parse(str)
        date = Time.parse(date.to_s)
      else
        date = Time.at(str.to_i / 1000)
      end
      date = date.in_time_zone(@user_settings[:timezone]).strftime(DATE_TIME_FORMAT_MAPPING[@user_settings[:preffered_date_format]])
      date += " #{@timezone_abbreviations[@user_settings[:timezone]]}"
    rescue
      nil
    end
  end

  def parse_date(str)
    return nil if str.blank?
    str = str.to_s
    date = ''

    begin
      if str.include?('-')
        date = Time.parse(str)
      else
        date = Time.at(str.to_i / 1000)
      end

      date.in_time_zone(@user_settings[:timezone]).strftime(DATE_FORMAT_MAPPING[@user_settings[:preffered_date_format]])
    rescue => e
      Rails.logger.error("Date parsing failed: #{e.message}")
      nil
    end
  end

  def current_entity
    (@child_entity_type.presence || @entity_type).camelize
  end
end
