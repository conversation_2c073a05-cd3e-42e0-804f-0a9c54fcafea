module V2
  class EmailSearchResultsSerializer
    prepend SimpleCommand
    include RecordActionsProcessor

    def initialize(emails, view = nil, sort = nil)
      @email_paginated_data = emails
      sort_order = sort&.split(',')&.last == 'asc' ? 'asc' : 'desc'
      email_ids = emails.collect(&:id)

      # PERFORMANCE: Main email query with optimizations
      # - Uses includes() to prevent N+1 queries for associations (email_thread, sender, owner)
      # - Uses select() to limit columns and reduce memory usage
      # - WHERE id IN (...) uses primary key index - efficient for moderate result sets
      # - ORDER BY created_at uses index_email_on_created_at
      # POTENTIAL ISSUE: Large email_ids arrays (>1000) can slow down IN clause
      @emails = Email.where(id: email_ids)
                .includes(:email_thread, :sender, :owner)
                .select(:tenant_id, :to, :cc, :bcc, :from, :email_thread_id,
                :sender_id, :owner_id, :subject, :status, :tracking_enabled,
                :body_summary, :read_by, :id, :created_at, :direction, :connected_account_id, :failed_reason)
                .order(created_at: sort_order.to_sym)
      binding.pry
      @auth_data = GetSecurityContext.call.result
      @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)
      @view = view
    end

    def call
      return nil unless @emails

      return bulk_response if @view.eql?('bulk')

      # Cache email IDs and thread IDs to avoid multiple pluck calls
      @email_ids = @emails.pluck(:id)
      @email_thread_ids = @emails.pluck(:email_thread_id).compact.uniq

      preload_emails_attachments_count
      preload_to_details
      preload_email_thread_read_details
      preload_subjects_for_thread
      preload_track_logs
      preload_link_logs
      preload_related_entities

      json = Jbuilder.new
      json.body do
        json.content(@emails) do |email|
          json.id email.email_thread_id
          json.sourceThreadId email.email_thread&.source_thread_id
          json.emailsCount get_emails_count(email)
          json.owner UserSerializer.call(email.owner).result
          json.recentEmail do
            json.id email.id
            json.from LookUpSerializer.call(email.sender).result
            json.to [LookUpSerializer.call(get_to_details(email)).result]
            json.associatedTo get_related_entities(email).map { |entity| LookUpSerializer.call(entity).result }
            json.subject get_subject(email.email_thread_id)
            json.bodySummary email.body_summary
            json.status email.status
            json.trackingEnabled email.tracking_enabled
            json.sentAt email.created_at
            json.read read?(email.email_thread_id)
            json.attachmentCount get_attachment_count(email)
            json.ownerId email.owner_id
            json.openedAt get_opened_at(email)
            json.recentLinkClickedAt get_recent_link_clicked_at(email)
            json.direction email.direction
            json.recordActions email_record_actions(email)
            json.failedReason email.failed_reason
          end
          json.recordActions thread_record_actions(email)
        end

        json.page do
          json.no @email_paginated_data.current_page.to_i
          json.size @email_paginated_data.per_page
        end
        json.totalElements @email_paginated_data.total_entries
        json.totalPages @email_paginated_data.total_pages
        json.first @email_paginated_data.previous_page.nil?
        json.last @email_paginated_data.next_page.nil?
      end
    end

    def bulk_response
      json = Jbuilder.new
      json.body do
        json.content(@emails) do |email|
          json.id email.email_thread_id
        end

        json.page do
          json.no @email_paginated_data.current_page.to_i
          json.size @email_paginated_data.per_page
        end
        json.totalElements @email_paginated_data.total_entries
        json.totalPages @email_paginated_data.total_pages
        json.first @email_paginated_data.previous_page.nil?
        json.last @email_paginated_data.next_page.nil?
      end
    end

    private

    def preload_emails_attachments_count
      if @email_thread_ids.blank?
        @email_thread_emails_count = {}
        @email_thread_attachments_count = {}
        return
      end

      # PERFORMANCE: Email count query - EXCELLENT performance
      # - Uses composite indexes: emails_tenant_id + index_emails_on_email_thread_id
      # - Single aggregation query prevents N+1 problem
      # - select(:id) minimizes data transfer
      # ESTIMATED: ~10-50ms for 100-1000 threads
      @email_thread_emails_count = Email.select(:id).where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids).group(:email_thread_id).count

      # PERFORMANCE: Attachment count query - GOOD performance
      # - Uses same efficient indexing as email count
      # - Leverages denormalized external_attachment_count field (avoids JOIN with attachments table)
      # OPTIMIZATION OPPORTUNITY: Add composite index (tenant_id, email_thread_id, external_attachment_count)
      # ESTIMATED: ~10-50ms for 100-1000 threads
      @email_thread_attachments_count = Email.select(:id).where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids).group(:email_thread_id).sum(:external_attachment_count)
    end

    def preload_to_details
      return @to_details_per_email = {} if @email_ids.blank?

      # PERFORMANCE: EmailLookUp query - GOOD but can be optimized
      # - Uses composite index: emaillookups_emailidtenantid (tenant_id, email_id)
      # - DISTINCT ON with ORDER BY can be expensive for large datasets
      # ISSUE: recipient_type IN (0,1,2) not indexed - requires table scan
      # OPTIMIZATION: Add index on (tenant_id, email_id, recipient_type)
      # ESTIMATED: ~50-200ms for 100-1000 emails
      intermediate_email_look_ups = EmailLookUp
        .select("DISTINCT ON (email_look_ups.email_id) email_look_ups.id, email_look_ups.email_id, email_look_ups.recipient_type, email_look_ups.look_up_id")
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids, recipient_type: [:to, :cc, :bcc])
        .order("email_look_ups.email_id, email_look_ups.recipient_type")

      look_up_ids = intermediate_email_look_ups.map(&:look_up_id).compact.uniq

      # PERFORMANCE: LookUp batch query - GOOD performance
      # - Uses primary key index for id IN (...)
      # - Has index_look_ups_on_tenant_id for tenant filtering
      # OPTIMIZATION: Consider composite index (tenant_id, id) for better performance
      # ESTIMATED: ~10-30ms for 100-1000 lookups
      to_look_ups = LookUp.where(tenant_id: @auth_data.tenant_id, id: look_up_ids).index_by(&:id)

      @to_details_per_email = intermediate_email_look_ups.each_with_object({}) do |elu, hash|
        hash[elu.email_id] = to_look_ups[elu.look_up_id] if elu.look_up_id && to_look_ups[elu.look_up_id]
      end
    end

    def preload_email_thread_read_details
      return @email_thread_read_tally = {} if @email_thread_ids.blank?

      # PERFORMANCE: Read status query - MAJOR PERFORMANCE ISSUE
      # - Uses tenant_id and email_thread_id indexes efficiently
      # CRITICAL ISSUE: 'read_by @> ARRAY[?]' requires full array scan on each row
      # - PostgreSQL cannot use regular indexes for array containment (@>) operations
      # SOLUTION: Add GIN index on read_by array field
      # RECOMMENDED: CREATE INDEX CONCURRENTLY idx_emails_read_by_gin ON emails USING GIN (read_by);
      # CURRENT ESTIMATED: ~500ms-2s for 1000 emails | WITH GIN INDEX: ~20-50ms
      @email_thread_read_tally =
        Email
        .select(:email_thread_id)
        .where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids)
        .where('read_by @> ARRAY[?]::bigint[]', [@auth_data.user_id])
        .map(&:email_thread_id)
        .tally
    end

    def preload_subjects_for_thread
      return @subjects_mapping = {} if @email_thread_ids.blank?

      # PERFORMANCE: Subject query - MODERATE performance concern
      # - Uses existing indexes efficiently (tenant_id, email_thread_id, created_at)
      # ISSUE: DISTINCT ON with different ORDER BY field can be expensive
      # - PostgreSQL needs to sort all matching rows to apply DISTINCT ON correctly
      # OPTIMIZATION: Add composite index (tenant_id, email_thread_id, created_at DESC)
      # ESTIMATED: ~50-150ms for 100-1000 threads
      @subjects_mapping = Email
        .select("DISTINCT ON (emails.email_thread_id) emails.email_thread_id, emails.subject")
        .where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids)
        .order(created_at: :desc)
        .pluck(:email_thread_id, :subject)
        .to_h
    end

    def get_to_details(email)
      @to_details_per_email[email.id]
    end

    def preload_track_logs
      return @opened_at_per_email = {} if @email_ids.blank?

      # PERFORMANCE: Track logs query - GOOD performance
      # - Has indexes on email_id, tenant_id, and created_at
      # - DISTINCT ON with matching ORDER BY is efficient (PostgreSQL optimized pattern)
      # - Uses index_email_track_logs_on_email_id and index_email_track_logs_on_tenant_id
      # OPTIMIZATION: Add composite index (tenant_id, email_id, created_at DESC) for better performance
      # ESTIMATED: ~30-80ms for 100-1000 emails
      track_logs = EmailTrackLog
        .select("DISTINCT ON (email_id) email_id, created_at")
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids)
        .order("email_id, created_at DESC")
        .to_a

      @opened_at_per_email = track_logs.each_with_object({}) do |log, hash|
        hash[log.email_id] = log.created_at
      end
    end

    def preload_link_logs
      return @link_logs_per_email = {} if @email_ids.blank?

      # PERFORMANCE: Link logs query - GOOD performance (similar to track logs)
      # - Has indexes on email_id, tenant_id, and created_at
      # - DISTINCT ON with matching ORDER BY is efficient
      # OPTIMIZATION: Add composite index (tenant_id, email_id, created_at DESC)
      # ESTIMATED: ~30-80ms for 100-1000 emails
      link_logs = EmailLinkLog
        .select("DISTINCT ON (email_id) email_id, link_mapping_id, created_at")
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids)
        .order("email_id, created_at DESC")
        .to_a

      link_mapping_ids = link_logs.map(&:link_mapping_id).compact.uniq

      # PERFORMANCE: LinkMapping batch query - EXCELLENT performance
      # - Uses primary key (UUID) index for id IN (...)
      # - Very efficient batch lookup
      # ESTIMATED: ~5-15ms for 100-1000 mappings
      link_mappings = LinkMapping.where(id: link_mapping_ids).index_by(&:id) if link_mapping_ids.any?

      @link_logs_per_email = link_logs.each_with_object({}) do |log, hash|
        mapping = link_mappings&.dig(log.link_mapping_id) if log.link_mapping_id
        if mapping
          hash[log.email_id] = {
            id: mapping.id,
            url: mapping.url,
            clickedAt: log.created_at
          }
        end
      end
    end

    def preload_related_entities
      return @related_entities_per_email = {} if @email_ids.blank?

      # PERFORMANCE: Related entities query - MODERATE performance issues
      # - Uses composite index emaillookups_emailidtenantid (tenant_id, email_id)
      # - JOIN with look_ups table adds complexity
      # ISSUES:
      #   1. No index on 'related' field - requires table scan
      #   2. No index on look_ups.entity_type - requires table scan on joined table
      #   3. WHERE conditions on joined table prevent efficient index usage
      # OPTIMIZATIONS NEEDED:
      #   - CREATE INDEX ON email_look_ups (tenant_id, related, email_id) WHERE related = true;
      #   - CREATE INDEX ON look_ups (entity_type, deleted, tenant_id) WHERE deleted = false;
      # ESTIMATED: ~100-300ms for 100-1000 emails | WITH INDEXES: ~30-80ms
      email_look_ups = EmailLookUp
        .joins(:look_up)
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids, related: true)
        .where(look_ups: { entity_type: [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT], deleted: false })
        .includes(:look_up)

      @related_entities_per_email = email_look_ups.each_with_object({}) do |elu, hash|
        hash[elu.email_id] ||= []
        hash[elu.email_id] << elu.look_up if elu.look_up
      end
    end

    def get_related_entities(email)
      @related_entities_per_email[email.id] || []
    end

    def get_emails_count(email)
      return 0 unless email.email_thread_id.present?
      email_count = @email_thread_emails_count[email.email_thread_id]
      email_count.zero? ? 0 : email_count - 1
    end

    def get_attachment_count(email)
      return 0 if email.email_thread_id.blank?

      @email_thread_attachments_count[email.email_thread_id] || 0
    end

    def read?(thread_id)
      thread_email_read_count = @email_thread_read_tally[thread_id]
      if thread_email_read_count.nil?
        return false
      elsif thread_email_read_count == @email_thread_emails_count[thread_id]
        return true
      else
        return false
      end
    end

    def get_subject(thread_id)
      @subjects_mapping[thread_id]
    end

    def get_opened_at(email)
      @opened_at_per_email[email.id]
    end

    def get_recent_link_clicked_at(email)
      @link_logs_per_email[email.id] || {}
    end
  end
end
