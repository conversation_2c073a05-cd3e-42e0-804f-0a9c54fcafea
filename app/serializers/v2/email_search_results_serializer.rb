module V2
  class EmailSearchResultsSerializer
    prepend SimpleCommand
    include RecordActionsProcessor

    def initialize(emails, view = nil, sort = nil)
      @email_paginated_data = emails
      sort_order = sort&.split(',')&.last == 'asc' ? 'asc' : 'desc'
      email_ids = emails.collect(&:id)
      @emails = Email.where(id: email_ids)
                .includes(:email_thread, :sender, :owner)
                .select(:tenant_id, :to, :cc, :bcc, :from, :email_thread_id,
                :sender_id, :owner_id, :subject, :status, :tracking_enabled,
                :body_summary, :read_by, :id, :created_at, :direction, :connected_account_id, :failed_reason)
                .order(created_at: sort_order.to_sym)
      binding.pry
      @auth_data = GetSecurityContext.call.result
      @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)
      @view = view
    end

    def call
      return nil unless @emails

      return bulk_response if @view.eql?('bulk')

      # Cache email IDs and thread IDs to avoid multiple pluck calls
      @email_ids = @emails.pluck(:id)
      @email_thread_ids = @emails.pluck(:email_thread_id).compact.uniq

      preload_emails_attachments_count
      preload_to_details
      preload_email_thread_read_details
      preload_subjects_for_thread
      preload_track_logs
      preload_link_logs
      preload_related_entities

      json = Jbuilder.new
      json.body do
        json.content(@emails) do |email|
          json.id email.email_thread_id
          json.sourceThreadId email.email_thread&.source_thread_id
          json.emailsCount get_emails_count(email)
          json.owner UserSerializer.call(email.owner).result
          json.recentEmail do
            json.id email.id
            json.from LookUpSerializer.call(email.sender).result
            json.to [LookUpSerializer.call(get_to_details(email)).result]
            json.associatedTo get_related_entities(email).map { |entity| LookUpSerializer.call(entity).result }
            json.subject get_subject(email.email_thread_id)
            json.bodySummary email.body_summary
            json.status email.status
            json.trackingEnabled email.tracking_enabled
            json.sentAt email.created_at
            json.read read?(email.email_thread_id)
            json.attachmentCount get_attachment_count(email)
            json.ownerId email.owner_id
            json.openedAt get_opened_at(email)
            json.recentLinkClickedAt get_recent_link_clicked_at(email)
            json.direction email.direction
            json.recordActions email_record_actions(email)
            json.failedReason email.failed_reason
          end
          json.recordActions thread_record_actions(email)
        end

        json.page do
          json.no @email_paginated_data.current_page.to_i
          json.size @email_paginated_data.per_page
        end
        json.totalElements @email_paginated_data.total_entries
        json.totalPages @email_paginated_data.total_pages
        json.first @email_paginated_data.previous_page.nil?
        json.last @email_paginated_data.next_page.nil?
      end
    end

    def bulk_response
      json = Jbuilder.new
      json.body do
        json.content(@emails) do |email|
          json.id email.email_thread_id
        end

        json.page do
          json.no @email_paginated_data.current_page.to_i
          json.size @email_paginated_data.per_page
        end
        json.totalElements @email_paginated_data.total_entries
        json.totalPages @email_paginated_data.total_pages
        json.first @email_paginated_data.previous_page.nil?
        json.last @email_paginated_data.next_page.nil?
      end
    end

    private

    def preload_emails_attachments_count
      if @email_thread_ids.blank?
        @email_thread_emails_count = {}
        @email_thread_attachments_count = {}
        return
      end
      
      @email_thread_emails_count = Email.select(:id).where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids).group(:email_thread_id).count

      @email_thread_attachments_count = Email.select(:id).where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids).group(:email_thread_id).sum(:external_attachment_count)
    end

    def preload_to_details
      return @to_details_per_email = {} if @email_ids.blank?
      
      intermediate_email_look_ups = EmailLookUp
        .select("DISTINCT ON (email_look_ups.email_id) email_look_ups.id, email_look_ups.email_id, email_look_ups.recipient_type, email_look_ups.look_up_id")
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids, recipient_type: [:to, :cc, :bcc])
        .order("email_look_ups.email_id, email_look_ups.recipient_type")
      
      look_up_ids = intermediate_email_look_ups.map(&:look_up_id).compact.uniq
      to_look_ups = LookUp.where(tenant_id: @auth_data.tenant_id, id: look_up_ids).index_by(&:id)
      
      @to_details_per_email = intermediate_email_look_ups.each_with_object({}) do |elu, hash|
        hash[elu.email_id] = to_look_ups[elu.look_up_id] if elu.look_up_id && to_look_ups[elu.look_up_id]
      end
    end

    def preload_email_thread_read_details
      return @email_thread_read_tally = {} if @email_thread_ids.blank?
      
      @email_thread_read_tally =
        Email
        .select(:email_thread_id)
        .where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids)
        .where('read_by @> ARRAY[?]::bigint[]', [@auth_data.user_id])
        .map(&:email_thread_id)
        .tally
    end

    def preload_subjects_for_thread
      return @subjects_mapping = {} if @email_thread_ids.blank?
      
      @subjects_mapping = Email
        .select("DISTINCT ON (emails.email_thread_id) emails.email_thread_id, emails.subject")
        .where(tenant_id: @auth_data.tenant_id, email_thread_id: @email_thread_ids)
        .order(created_at: :desc)
        .pluck(:email_thread_id, :subject)
        .to_h
    end

    def get_to_details(email)
      @to_details_per_email[email.id]
    end

    def preload_track_logs
      return @opened_at_per_email = {} if @email_ids.blank?
      
      # Get the most recent track log per email using DISTINCT ON
      track_logs = EmailTrackLog
        .select("DISTINCT ON (email_id) email_id, created_at")
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids)
        .order("email_id, created_at DESC")
        .to_a
      
      @opened_at_per_email = track_logs.each_with_object({}) do |log, hash|
        hash[log.email_id] = log.created_at
      end
    end

    def preload_link_logs
      return @link_logs_per_email = {} if @email_ids.blank?
      
      # Get the most recent link log per email
      link_logs = EmailLinkLog
        .select("DISTINCT ON (email_id) email_id, link_mapping_id, created_at")
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids)
        .order("email_id, created_at DESC")
        .to_a
      
      link_mapping_ids = link_logs.map(&:link_mapping_id).compact.uniq
      link_mappings = LinkMapping.where(id: link_mapping_ids).index_by(&:id) if link_mapping_ids.any?
      
      @link_logs_per_email = link_logs.each_with_object({}) do |log, hash|
        mapping = link_mappings&.dig(log.link_mapping_id) if log.link_mapping_id
        if mapping
          hash[log.email_id] = {
            id: mapping.id,
            url: mapping.url,
            clickedAt: log.created_at
          }
        end
      end
    end

    def preload_related_entities
      return @related_entities_per_email = {} if @email_ids.blank?
      
      email_look_ups = EmailLookUp
        .joins(:look_up)
        .where(tenant_id: @auth_data.tenant_id, email_id: @email_ids, related: true)
        .where(look_ups: { entity_type: [LOOKUP_LEAD, LOOKUP_DEAL, LOOKUP_CONTACT], deleted: false })
        .includes(:look_up)
      
      @related_entities_per_email = email_look_ups.each_with_object({}) do |elu, hash|
        hash[elu.email_id] ||= []
        hash[elu.email_id] << elu.look_up if elu.look_up
      end
    end

    def get_related_entities(email)
      @related_entities_per_email[email.id] || []
    end

    def get_emails_count(email)
      return 0 unless email.email_thread_id.present?
      email_count = @email_thread_emails_count[email.email_thread_id]
      email_count.zero? ? 0 : email_count - 1
    end

    def get_attachment_count(email)
      return 0 if email.email_thread_id.blank?

      @email_thread_attachments_count[email.email_thread_id] || 0
    end

    def read?(thread_id)
      thread_email_read_count = @email_thread_read_tally[thread_id]
      if thread_email_read_count.nil?
        return false
      elsif thread_email_read_count == @email_thread_emails_count[thread_id]
        return true
      else
        return false
      end
    end

    def get_subject(thread_id)
      @subjects_mapping[thread_id]
    end

    def get_opened_at(email)
      @opened_at_per_email[email.id]
    end

    def get_recent_link_clicked_at(email)
      @link_logs_per_email[email.id] || {}
    end
  end
end
