module V1
  class EmailSearchResultsSerializer
    prepend SimpleCommand
    include RecordActionsProcessor

    def initialize(emails, sort = nil)
      @email_paginated_data = emails
      sort_order = sort&.split(',')&.last == 'asc' ? 'asc' : 'desc'
      @emails = Email.where(id: emails.collect(&:id)).
        select(:tenant_id, :to, :cc, :bcc, :from, :email_thread_id,
               :sender_id, :owner_id, :subject, :status, :tracking_enabled,
               :body_summary, :read_by, :id, :created_at, :direction, :connected_account_id, :failed_reason).order(created_at: sort_order.to_sym)

      @auth_data = GetSecurityContext.call.result
      @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)
    end

    def call
      return nil unless @emails

      preload_emails_attachments_count
      preload_to_details
      preload_email_thread_read_details
      preload_subjects_for_thread

      json = Jbuilder.new
      json.body do
        json.content(@emails) do |email|
          json.id email.id
          json.threadId email.email_thread_id
          json.count get_emails_count(email)
          json.from LookUpSerializer.call(email.sender).result
          json.to [LookUpSerializer.call(get_to_details(email)).result]
          json.associatedTo get_related_entites(email).map { |entity| LookUpSerializer.call(entity).result }
          json.subject get_subject(email.email_thread_id)
          json.bodySummary email.body_summary
          json.status email.status
          json.trackingEnabled email.tracking_enabled
          json.sentAt email.created_at
          json.read read?(email.email_thread_id)
          json.attachmentCount get_attachment_count(email)
          json.ownerId email.owner_id
          json.openedAt email.email_track_logs.order(created_at: :desc).first.try(:created_at)
          json.recentLinkClickedAt recent_link_details email
          json.direction email.direction
          json.recordActions email_record_actions(email)
          json.failedReason email.failed_reason
        end

        json.page do
          json.no @email_paginated_data.current_page.to_i
          json.size @email_paginated_data.per_page
        end
        json.totalElements @email_paginated_data.total_entries
        json.totalPages @email_paginated_data.total_pages
        json.first @email_paginated_data.previous_page.nil?
        json.last @email_paginated_data.next_page.nil?
      end
    end

    private

    def preload_emails_attachments_count
      @email_thread_emails_count = Email.select(:id).where(tenant_id: @auth_data.tenant_id, email_thread_id: @emails.pluck(:email_thread_id)).group(:email_thread_id).count

      @email_thread_attachments_count = Email.select(:id).where(tenant_id: @auth_data.tenant_id, email_thread_id: @emails.pluck(:email_thread_id)).group(:email_thread_id).sum(:external_attachment_count)
    end

    def preload_to_details
      intermediate_email_look_ups =  EmailLookUp.select("DISTINCT ON (email_look_ups.email_id) email_look_ups.id, email_look_ups.email_id, email_look_ups.recipient_type, email_look_ups.look_up_id").where(tenant_id: @auth_data.tenant_id, email_id: @emails.pluck(:id), recipient_type: [:to, :cc, :bcc]).order("email_look_ups.email_id, email_look_ups.recipient_type")
      to_look_ups = LookUp.where(tenant_id: @auth_data.tenant_id, id: intermediate_email_look_ups.map(&:look_up_id))
      @to_details_per_email = intermediate_email_look_ups.map { |elu| [elu.email_id, to_look_ups.find { |lu| lu.id == elu.look_up_id }] }.to_h
    end

    def preload_email_thread_read_details
      @email_thread_read_tally =
        Email
          .select(:email_thread_id)
          .where(tenant_id: @auth_data.tenant_id, email_thread_id: @emails.pluck(:email_thread_id))
          .where('read_by @> ARRAY[?]::bigint[]', [@auth_data.user_id])
          .map(&:email_thread_id)
          .tally
    end

    def preload_subjects_for_thread
      @subjects_mapping = Email.select("DISTINCT ON (emails.email_thread_id) emails.email_thread_id, emails.subject").where(tenant_id: @auth_data.tenant_id, email_thread_id: @emails.pluck(:email_thread_id)).order(created_at: :desc).pluck(:email_thread_id, :subject).to_h
    end

    def get_to_details(email)
      @to_details_per_email[email.id]
    end

    def get_related_entites(email)
      email.related_to.select { |look_up| look_up.is_a_lead? ||look_up.is_a_deal? || look_up.is_a_contact? }
    end

    def get_emails_count(email)
      return 0 unless email.email_thread_id.present?
      email_count = @email_thread_emails_count[email.email_thread_id]
      email_count.zero? ? 0 : email_count - 1
    end

    def get_attachment_count(email)
      return 0 if email.email_thread_id.blank?

      @email_thread_attachments_count[email.email_thread_id] || 0
    end

    def read?(thread_id)
      thread_email_read_count = @email_thread_read_tally[thread_id]
      if thread_email_read_count.nil?
        return false
      elsif thread_email_read_count == @email_thread_emails_count[thread_id]
        return true
      else
        return false
      end
    end

    def get_subject(thread_id)
      @subjects_mapping[thread_id]
    end

    def recent_link_details email
      link_log = email.email_link_logs.order(created_at: :desc)&.first
      return {} unless link_log
      { id: link_log.link_mapping.id, url: link_log.link_mapping.url, clickedAt: link_log.created_at }
    end
  end
end
