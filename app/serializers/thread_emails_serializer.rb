class ThreadEmailsSerializer
  prepend SimpleCommand
  include RecordActionsProcessor

  def initialize(emails)
    @emails = emails
  end

  def call
    return nil unless @emails
    @auth_data = Thread.current[:auth]
    @connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, user_id: @auth_data.user_id, active: true)

    json = Jbuilder.new
    json.body do
      json.content(@emails) do |email|
        json.messageId email.id
        json.from LookUpSerializer.call(email.sender).result
        json.to email.to_recipients.collect{ |r| LookUpSerializer.call(r).result }
        json.cc email.cc_recipients.collect{ |r| LookUpSerializer.call(r).result }
        json.bcc email.bcc_recipients.collect{ |r| LookUpSerializer.call(r).result }
        json.associatedTo get_related_entites(email).map { |entity| LookUpSerializer.call(entity).result }
        json.subject email.subject.present? ? email.subject : 'No Subject'
        json.body email.body
        json.sentAt email.created_at
        json.read email.read?
        json.status email.status
        json.trackingEnabled email.tracking_enabled
        json.attachments get_attachments email
        json.openedAt email.email_track_logs.pluck(:created_at)
        json.ownerId email.owner_id
        json.linksClickedAt link_details email
        json.recordActions email_record_actions(email)
        json.failedReason email.failed_reason
      end
      
      json.sourceThreadId @emails.first.email_thread&.source_thread_id

      json.page do
        json.no @emails.current_page.to_i
        json.size @emails.per_page
      end
      json.totalElements @emails.total_entries
      json.totalPages @emails.total_pages
      json.first @emails.previous_page.nil?
      json.last @emails.next_page.nil?
      json.ownerId @emails.first&.owner_id
      json.owner @emails.first.present? ? UserSerializer.call(@emails.first.owner).result : nil
    end
  end

  private

  def get_to_details(email)
    l_email = email.to.first
    l_email = email.cc.first unless l_email
    lookup = LookUp.find_by_email(l_email) if l_email
  end

  def get_related_entites(email)
    email.related_to.select { |look_up| look_up.is_a_lead? ||look_up.is_a_deal? || look_up.is_a_contact? }
  end

  def get_attachments email
    email.attachments.where(inline: false).map{|attachment| { id: attachment.id, fileName: attachment.extract_file_name(false), fileSize: attachment.file_size.to_i} }
  end

  def link_details email
    res = []
    email.link_mappings.each do |link_mapping|
      if link_mapping.email_link_logs.exists?
        res << { id: link_mapping.id, url: CGI.unescape(link_mapping.url), clickedAt: link_mapping.email_link_logs.order(created_at: :desc).pluck(:created_at) }
      end
    end
    res
  end
end
